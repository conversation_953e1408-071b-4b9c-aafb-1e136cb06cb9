// Content script that automatically injects MSW into monitora-frontend
// This runs before the page loads, allowing us to mock WebSocket before the app initializes

console.log('[Monitora WebSocket Mocker] Content script loaded')

// Check if this is a monitora frontend page
if (window.location.hostname === 'localhost' && window.location.port === '3000') {
  console.log('[Monitora WebSocket Mocker] Detected monitora-frontend, injecting MSW...')
  
  // Inject MSW setup script
  const script = document.createElement('script')
  script.src = chrome.runtime.getURL('msw-browser.js')
  script.type = 'module'
  script.onload = () => {
    console.log('[Monitora WebSocket Mocker] MSW browser script loaded')
    
    // Auto-enable WebSocket mocking
    const enableScript = document.createElement('script')
    enableScript.textContent = `
      // Wait for MSW to be available
      const checkMSW = setInterval(() => {
        if (window.enableWebSocketMocking) {
          console.log('[Monitora WebSocket Mocker] Auto-enabling WebSocket mocking')
          window.enableWebSocketMocking()
          
          // Add test functions to window
          window.sendTestNotification = () => {
            if (window.sendMockNotification) {
              window.sendMockNotification({
                model: "AppNotificationsLog",
                method: "CREATE",
                data: [{
                  id: Date.now(),
                  app_notification_type: 1,
                  title: "Auto-Injected MSW Test",
                  icon: "info",
                  url: null,
                  created: new Date().toISOString(),
                  is_read: false
                }]
              })
            }
          }
          
          window.checkMSWStatus = () => {
            if (window.getMockStatus) {
              console.log('MSW Status:', window.getMockStatus())
            }
          }
          
          clearInterval(checkMSW)
        }
      }, 100)
      
      // Stop checking after 10 seconds
      setTimeout(() => clearInterval(checkMSW), 10000)
    `
    document.head.appendChild(enableScript)
  }
  
  // Inject the script before any other scripts load
  if (document.head) {
    document.head.appendChild(script)
  } else {
    document.addEventListener('DOMContentLoaded', () => {
      document.head.appendChild(script)
    })
  }
  
  // Copy service worker to the page
  fetch(chrome.runtime.getURL('mockServiceWorker.js'))
    .then(response => response.text())
    .then(content => {
      // Create a blob URL for the service worker
      const blob = new Blob([content], { type: 'application/javascript' })
      const serviceWorkerUrl = URL.createObjectURL(blob)
      
      // Register the service worker
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register(serviceWorkerUrl)
          .then(() => console.log('[Monitora WebSocket Mocker] Service worker registered'))
          .catch(err => console.error('[Monitora WebSocket Mocker] Service worker registration failed:', err))
      }
    })
    .catch(err => console.error('[Monitora WebSocket Mocker] Failed to load service worker:', err))
}
