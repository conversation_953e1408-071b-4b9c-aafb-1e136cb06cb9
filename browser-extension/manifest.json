{"manifest_version": 3, "name": "Monitora WebSocket Mocker", "version": "1.0.0", "description": "Automatically mocks WebSocket connections for Monitora frontend development", "permissions": ["activeTab", "scripting"], "host_permissions": ["http://localhost:3000/*", "http://localhost:*/*"], "content_scripts": [{"matches": ["http://localhost:3000/*", "http://localhost:*/*"], "js": ["content-script.js"], "run_at": "document_start"}], "web_accessible_resources": [{"resources": ["msw-browser.js", "mockServiceWorker.js"], "matches": ["http://localhost:*/*"]}], "action": {"default_popup": "popup.html", "default_title": "Toggle WebSocket Mocking"}}