# MSW Integration Guide

This branch provides a minimalistic Mock Service Worker (MSW) solution for testing WebSocket notifications without running the actual WebSocket server.

## What's Included

- **MSW dependency**: `msw 2.10.2` for API mocking
- **Test script**: `scripts/test-msw.js` with inline handlers
- **Package script**: `pnpm test:msw` to run MSW tests

## Usage

### Run MSW Tests

```bash
pnpm test:msw
```

This will:
1. Start MSW server with mocked endpoints
2. Test health, notification, WebSocket connection, and message history endpoints
3. Display test results and stop the server

### Integration with Frontend

To use MSW in your frontend tests:

1. Install MSW in your frontend project:
```bash
pnpm add -D msw
```

2. Copy the handlers from `scripts/test-msw.js` to your frontend test setup

3. Use MSW to mock WebSocket server responses during testing

## Endpoints Mocked

- `GET /health` - Health check with connection count
- `POST /notify` - Send notifications (stores in memory)
- `POST /ws/connect` - Simulate WebSocket connections
- `GET /messages` - Retrieve message history

## Message Format

All notification messages follow the format:
```json
{
  "model": "AppNotificationsLog",
  "method": "CREATE",
  "data": [...]
}
```

This matches the existing WebSocket server implementation.
