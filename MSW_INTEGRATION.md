# MSW Integration Guide

This branch provides a minimalistic Mock Service Worker (MSW) solution for testing WebSocket notifications without running the actual WebSocket server.

## What's Included

- **MSW dependency**: `msw 2.10.2` for API mocking
- **Test script**: `scripts/test-msw.js` with inline handlers
- **Package script**: `pnpm test:msw` to run MSW tests

## Usage

### Run MSW Tests

```bash
pnpm test:msw
```

This will:
1. Start MSW server with mocked endpoints
2. Test health, notification, WebSocket connection, and message history endpoints
3. Display test results and stop the server

### Integration with Frontend

#### Quick Setup for Monitora Frontend

1. **Copy files to monitora-frontend:**
```bash
# Copy MSW browser setup
cp src/msw-browser.js /path/to/monitora-frontend/src/

# Copy service worker
cp public/mockServiceWorker.js /path/to/monitora-frontend/public/
```

2. **Add to your frontend app** (in `_app.js` or main component):
```javascript
import { enableWebSocketMocking } from '~/src/msw-browser'

// Enable WebSocket mocking in development
if (process.env.NODE_ENV === 'development') {
  enableWebSocketMocking()
}
```

3. **Test integration:**
```bash
pnpm test:frontend
```

#### Frontend Testing Functions

Once integrated, use these browser console commands:

```javascript
// Send test notification
sendTestNotification()

// Check MSW status
checkMSWStatus()

// Send custom notification
sendMockNotification({
  model: "AppNotificationsLog",
  method: "CREATE",
  data: [{
    id: Date.now(),
    app_notification_type: 1,
    title: "Custom Test",
    icon: "info",
    url: null,
    created: new Date().toISOString(),
    is_read: false
  }]
})
```

#### How It Works

- **WebSocket Interception**: MSW replaces WebSocket constructor to intercept connections
- **Message Simulation**: `/notify` endpoint triggers WebSocket `onmessage` handler
- **No Code Changes**: Existing WebSocket code works without modifications
- **Development Only**: MSW only activates in development mode

## Endpoints Mocked

- `GET /health` - Health check with connection count
- `POST /notify` - Send notifications (stores in memory)
- `POST /ws/connect` - Simulate WebSocket connections
- `GET /messages` - Retrieve message history

## Message Format

All notification messages follow the format:
```json
{
  "model": "AppNotificationsLog",
  "method": "CREATE",
  "data": [...]
}
```

This matches the existing WebSocket server implementation.
