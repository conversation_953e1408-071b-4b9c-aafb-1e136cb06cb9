// Debug WebSocket Connection Script
// Run this in the browser's developer console to debug the WebSocket connection

console.log('🔍 WebSocket Connection Debug');
console.log('============================');

// Check if we're on the right page
if (typeof window !== 'undefined' && window.location) {
  console.log('Current URL:', window.location.href);
} else {
  console.log('❌ Not in browser environment');
}

// Try to access the app store
let appStore = null;
try {
  // Try different ways to access the app store
  if (window.appStore) {
    appStore = window.appStore;
  } else if (window.__NEXT_DATA__ && window.__NEXT_DATA__.props && window.__NEXT_DATA__.props.pageProps && window.__NEXT_DATA__.props.pageProps.appStore) {
    appStore = window.__NEXT_DATA__.props.pageProps.appStore;
  } else {
    // Try to find it in React DevTools
    const reactFiber = document.querySelector('#__next')._reactInternalFiber || document.querySelector('#__next')._reactInternals;
    if (reactFiber) {
      console.log('Found React fiber, searching for app store...');
    }
  }
} catch (error) {
  console.log('Error accessing app store:', error);
}

if (appStore) {
  console.log('✅ Found app store');
  
  // Check WebSocket URL
  const websocketUrl = appStore.account.websocket_url;
  console.log('WebSocket URL:', websocketUrl);
  
  if (websocketUrl) {
    console.log('✅ WebSocket URL is configured');
  } else {
    console.log('❌ WebSocket URL is NOT configured');
    console.log('This means the backend /init/ endpoint is not returning websocket_url');
  }
  
  // Check last WebSocket message
  const lastMessage = appStore.websocketMessages.lastMessage;
  console.log('Last WebSocket message:', lastMessage);
  
  // Check notifications
  const unreadCount = appStore.appNotifications.unreadCount;
  console.log('Unread notifications count:', unreadCount);
  
  // Check if user is logged in
  const isLogged = appStore.account.isLogged;
  console.log('User logged in:', isLogged);
  
  // Check account data
  console.log('Account data:', {
    isLoaded: appStore.account.isLoaded,
    user: appStore.account.user ? {
      id: appStore.account.user.id,
      email: appStore.account.user.email
    } : null,
    workspace: appStore.account.workspace ? {
      id: appStore.account.workspace.id,
      name: appStore.account.workspace.name
    } : null
  });
  
} else {
  console.log('❌ Could not find app store');
  console.log('Make sure you are on a Monitora app page and logged in');
}

// Function to manually set WebSocket URL for testing
window.setTestWebSocketUrl = function() {
  if (appStore) {
    console.log('🧪 Setting test WebSocket URL...');
    appStore.account.setParam('websocket_url', 'ws://localhost:8080/ws');
    console.log('✅ Test WebSocket URL set to: ws://localhost:8080/ws');
    console.log('The WebSocket should connect now. Check the browser console for "[MntrWebSocket] opened"');
  } else {
    console.log('❌ Cannot set WebSocket URL - app store not found');
  }
};

// Function to clear WebSocket URL
window.clearWebSocketUrl = function() {
  if (appStore) {
    console.log('🧹 Clearing WebSocket URL...');
    appStore.account.setParam('websocket_url', null);
    console.log('✅ WebSocket URL cleared');
  } else {
    console.log('❌ Cannot clear WebSocket URL - app store not found');
  }
};

// Function to check WebSocket server health
window.checkWebSocketServer = async function() {
  try {
    console.log('🏥 Checking WebSocket server health...');
    const response = await fetch('http://localhost:8080/health');
    const data = await response.json();
    console.log('WebSocket server health:', data);
    
    if (data.connections === 0) {
      console.log('⚠️  Server has 0 connections - frontend is not connected');
    } else {
      console.log(`✅ Server has ${data.connections} connection(s)`);
    }
  } catch (error) {
    console.log('❌ WebSocket server is not running or not accessible:', error);
    console.log('Make sure to start the server with: cd mediaboard-ws && npm run dev');
  }
};

console.log('');
console.log('🛠️  Available debug functions:');
console.log('- setTestWebSocketUrl() - Manually set WebSocket URL for testing');
console.log('- clearWebSocketUrl() - Clear the WebSocket URL');
console.log('- checkWebSocketServer() - Check if WebSocket server is running');
console.log('');
console.log('💡 Quick test: Run setTestWebSocketUrl() then check the console for WebSocket connection logs');
