/* eslint-disable playwright/no-standalone-expect */
import { expect, Page, PageAssertionsToHaveScreenshotOptions } from '@playwright/test'
import dotenv from 'dotenv'
import { User } from './pages.support'

dotenv.config({ path: 'e2e/artifacts/params.env' })

export type ScreenshotConfig = {
  include?: User[]
  options?: PageAssertionsToHaveScreenshotOptions
  afterScreenshot?: (page: Page) => Promise<void>
}

type TestBodyType = (page: Page, isMobile?: boolean) => Promise<void | (() => Promise<void>)>

type BaseTestType = {
  baseTestBody?: TestBodyType
  customerDefaultTestBody?: TestBodyType
  customerGlobalTestBody?: TestBodyType
  customerHeroTestBody?: TestBodyType
  customerPForbiddenTestBody?: TestBodyType
  customerPFullAccessTestBody?: TestBodyType
  customerPReadOnlyTestBody?: TestBodyType
  isAppRouter?: boolean
  pathname?: string
  screenshotConfig?: ScreenshotConfig
  tag?: string[]
}

export type StaffTestType = BaseTestType & {
  staffTestBody: (page: Page) => Promise<void>
}

export const pages = createPagesFixture({
  '/': {
    async customerDefaultTestBody(page) {
      await expect(page.getByText('Loading...')).toBeHidden()
      await expect(
        page
          .locator('div')
          .filter({ hasText: /^show_chart1 article$/ })
          .locator('i'),
      ).toBeVisible()
      await expect(
        page.getByRole('link', {
          name: 'Mazací tramvaj s formulí „na zádech“ brázdí Prahu. Láká na adrenalinovou show',
          exact: true,
        }),
      ).toBeVisible()
    },
    async customerGlobalTestBody(page) {
      await expect(page.getByRole('heading', { name: 'No results found' })).toBeVisible()
    },
    pathname:
      '/?lower_date=2024-08-01&query=Mazac%C3%AD%20tramvaj%20s%20formul%C3%AD%20%E2%80%9Ena%20z%C3%A1dech%E2%80%9C%20br%C3%A1zd%C3%AD%20Prahu.%20L%C3%A1k%C3%A1%20na%20adrenalinovou%20show&upper_date=2024-08-31',
    screenshotConfig: {
      include: ['guest', 'customerHero', 'customerGlobal', 'customerPReadOnly'],
      options: { fullPage: true },
    },
  },
  '/404': {
    async baseTestBody(page) {
      await expect404(page, '/404')
    },
    screenshotConfig: {
      include: ['customerDefault'],
    },
  },
  '/500': {
    async baseTestBody(page) {
      const response = await page.request.get('/500')
      await expect(response.status()).toBe(500)
      await expect(page.getByText('500 - This is fine... right?')).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerDefault'],
    },
  },
  '/analytics': {
    screenshotConfig: {
      include: ['customerHero'],
    },
  },
  '/app-notifications': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Notifications' })).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerDefault'],
    },
  },
  '/article/[articleId]/[token]': {
    async customerDefaultTestBody(page) {
      await expect(
        page.getByRole('heading', { name: 'Mazací tramvaj s formulí „na' }),
      ).toBeVisible()

      await expect(
        page.getByText(`V Praze projíždí speciální tramvaj "mazačka" s monopostem Formule 1, která láká fanoušky na akci Red Bull Showrun. Tramvaj je k vidění od 12. do 15. srpna 2024 a její trasa zahrnuje známé pražské zastávky. Samotná akce se uskuteční
17. srpna na Rohanském nábřeží, kde se diváci mohou těšit na představení ikonického vozu a další adrenalinové atrakce.`),
      ).toBeVisible()
    },
    async customerGlobalTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Invalid article link' })).toBeVisible()
    },
    pathname: process.env.E2E_PARAM_FEED_STORY_PATHNAME,
    screenshotConfig: {
      include: ['customerDefault', 'customerGlobal'],
    },
  },
  '/authors': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Medialist' })).toBeVisible()
    },
    async customerPFullAccessTestBody(page) {
      await expect(page.getByText('1000+').first()).toBeVisible()
      await expect(page.getByRole('link', { name: 'person_add' })).toBeVisible()
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page.getByText('1000+').first()).toBeVisible()
      await expect(page.getByRole('link', { name: 'person_add' })).toBeHidden()
    },
    screenshotConfig: {
      include: ['customerDefault', 'customerPReadOnly', 'customerPFullAccess'],
    },
  },
  '/authors/create': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Medialist' })).toBeVisible()
    },
    async customerPFullAccessTestBody(page) {
      await expect(page.getByText('About Author')).toBeVisible()
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
  },
  '/brand-tracking': {
    async customerDefaultTestBody(page) {
      await expect404(page, '/brand-tracking')
    },
    async customerPFullAccessTestBody(page, isMobile) {
      if (isMobile) {
        await expect(
          page.locator('iframe').contentFrame().getByText('Behavio Platform currently'),
        ).toBeVisible()
      } else {
        await expect(
          page.locator('iframe').contentFrame().getByRole('heading', { name: 'Netflix' }),
        ).toBeVisible()
        await expect(
          page.locator('iframe').contentFrame().getByRole('heading', { name: 'Key metrics' }),
        ).toBeVisible()
      }
    },
    isAppRouter: true,
  },
  '/crisis-communication': {
    async customerDefaultTestBody(page) {
      await expect(page.getByText('Immediate notifications')).toBeVisible()
    },
    async customerPFullAccessTestBody(page) {
      await expect(page.getByRole('heading', { name: 'No results found' })).toBeVisible()
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page.getByRole('heading', { name: 'No results found' })).toBeVisible()
    },
    pathname: '/crisis-communication?lower_date=2024-10-01&upper_date=2024-10-31',
    screenshotConfig: {
      include: ['customerDefault', 'customerPReadOnly'],
    },
  },
  '/emailing': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Emailing' })).toBeVisible()
    },
    async customerHeroTestBody(page) {
      await expect(page.getByRole('link', { name: 'E E2E Test Campaign: Toyota' })).toBeVisible()
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    async customerPFullAccessTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerDefault', 'customerHero', 'customerPReadOnly', 'customerPFullAccess'],
      options: { fullPage: true },
    },
  },
  '/emailing/activate-sender': {
    async baseTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
  },
  '/emailing/campaign/[campaignId]': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Emailing' })).toBeVisible()
    },
    async customerHeroTestBody(page) {
      await expect(page.getByRole('link', { name: 'E2E Test: Exclusive Toyota' })).toBeVisible()
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    async customerPFullAccessTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerHero'],
    },
  },
  '/emailing/campaign/[campaignId]/email/[emailId]': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Emailing' })).toBeVisible()
    },
    async customerHeroTestBody(page) {
      await expect(page.locator('#wrapper-container')).toBeVisible()
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    async customerPFullAccessTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerHero'],
    },
  },
  '/emailing/campaign/[campaignId]/email/[emailId]/recipients': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Emailing' })).toBeVisible()
    },
    async customerHeroTestBody(page) {
      await expect(page.getByRole('heading', { name: '<EMAIL>' })).toBeVisible()
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    async customerPFullAccessTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerHero'],
    },
  },
  '/emailing/campaign/[campaignId]/email/create': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Emailing' })).toBeVisible()
    },
    async customerHeroTestBody(page) {
      await expect(page.getByRole('main').getByRole('img')).toBeHidden()
      await expect(page.getByPlaceholder('Insert internal name of email')).toBeVisible({
        // Wait for init + senders + email create + redirect to edit/[emailId],
        // so again: senders + email + recipients
        // TODO: Shorten fetch/load window
        timeout: 10000,
      })

      return async () => {
        await page.getByRole('link', { name: 'chevron_left Back' }).click()
        await page.getByRole('button', { name: 'more_vert' }).nth(1).click() // TODO: Better selector
        await page.getByText('Delete', { exact: true }).click()
        const deleteResponsePromise = page.waitForResponse(
          (response) =>
            /\/emailing\/campaign\/\d+\/email\/\d+\//.test(response.url()) &&
            response.status() === 204 &&
            response.request().method() === 'DELETE',
        )
        await page.getByRole('button', { name: 'Remove' }).click()
        await deleteResponsePromise
      }
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
    async customerPFullAccessTestBody(page) {
      await expect(page.locator('circle')).toBeHidden()
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerHero'],
      options: { fullPage: true },
    },
  },
  '/emailing/campaign/[campaignId]/email/edit/[emailId]': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Emailing' })).toBeVisible()
    },
    async customerHeroTestBody(page) {
      await expect(
        page
          .locator('#wrapper-container iframe')
          .contentFrame()
          .getByRole('cell', { name: '🚗 E2E Test: Exclusive Toyota' }),
      ).toBeVisible()
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    async customerPFullAccessTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerHero'],
      options: { fullPage: true },
    },
  },
  '/emailing/campaign/[campaignId]/recipients': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Emailing' })).toBeVisible()
    },
    async customerHeroTestBody(page) {
      await expect(page.getByRole('heading', { name: '<EMAIL>' })).toBeVisible()
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    async customerPFullAccessTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerHero'],
    },
  },
  '/emailing/settings': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Emailing' })).toBeVisible()
    },
    async customerHeroTestBody(page) {
      await expect(
        page
          .locator('div')
          .filter({ hasText: /^Toyota test <martin\.stovicek\+test140@mediaboard\.com>$/ }),
      ).toBeVisible()
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
    async customerPFullAccessTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Initial Emailing settings' })).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerHero'],
    },
  },
  '/export': {
    async customerDefaultTestBody(page) {
      await expect(page.getByText('Export list is empty')).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerDefault'],
    },
  },
  '/external-analytics': {
    async customerDefaultTestBody(page) {
      await expect404(page, '/external-analytics')
    },
    isAppRouter: true,
  },
  '/export/history': {
    screenshotConfig: {
      include: ['customerDefault'],
    },
  },
  '/help/search': {
    async customerDefaultTestBody(page) {
      await expect(page.getByText('Penguin').first()).toBeVisible()
    },
    isAppRouter: true,
    screenshotConfig: {
      include: ['customerDefault'],
    },
  },
  '/reports': {
    async customerDefaultTestBody(page, isMobile) {
      await expect(page.getByRole('button', { name: 'add New report' })).toBeVisible()
      await expect(page.getByText('My topic')).toHaveCount(isMobile ? 1 : 2)
    },
    async customerHeroTestBody(page) {
      await expect(page.getByRole('button', { name: 'add New report' })).toBeVisible()
      await expect(page.getByText('No email reports created')).toBeVisible()
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page.getByRole('button', { name: 'add New report' })).toBeHidden()
      await expect(page.getByText('Email reports (1)')).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerDefault', 'customerPReadOnly'],
    },
  },
  '/reports/history': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Reports History' })).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerDefault'],
    },
  },
  '/sign-up': {
    async baseTestBody(page) {
      await expect(page.getByText('Sign Up')).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerDefault'],
    },
  },
  '/sign-up-completion': {
    async baseTestBody(page) {
      await expect(page.getByText('Sign Up', { exact: true })).toBeVisible()
      await expect(page.getByText('Phone')).toBeVisible() // Wait for redirect (on client side)
    },
  },
  '/staff/admin/customers': {
    async staffTestBody(page) {
      await expect(page.getByText('Customers')).toBeVisible()
      await expect(page.getByTestId('replace_content').first()).toBeVisible()
    },
    screenshotConfig: {},
  },
  '/staff/admin/customers/[customerId]/invoices': {
    async staffTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Mediaboard devel xx' })).toBeVisible({
        timeout: 10000, // TODO: Clean up playwright+sign_up_... users to speed up customer load
      })
      await expect(page.getByText('No data')).toBeVisible()
    },
    pathname: '/staff/admin/customers/14790/invoices',
    screenshotConfig: {},
  },
  '/staff/admin/customers/[customerId]/merged-customers': {
    async staffTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Mediaboard devel xx' })).toBeVisible({
        timeout: 10000, // TODO: Clean up playwright+sign_up_... users to speed up customer load
      })
      await expect(page.getByText('No data')).toBeVisible()
    },
    pathname: '/staff/admin/customers/14790/merged-customers',
    screenshotConfig: {},
  },
  '/staff/admin/customers/[customerId]/users': {
    async staffTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Mediaboard devel xx' })).toBeVisible({
        timeout: 10000, // TODO: Clean up playwright+sign_up_... users to speed up customer load
      })
      await expect(page.getByText('Users').filter({ has: page.getByText(/\(\d+\)/) })).toBeVisible()
    },
    pathname: '/staff/admin/customers/14790/users',
    screenshotConfig: {},
  },
  '/staff/admin/customers/[customerId]/expenses': {
    async staffTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Mediaboard devel xx' })).toBeVisible({
        timeout: 10000, // TODO: Clean up playwright+sign_up_... users to speed up customer load
      })
      await expect(page.getByText('No data')).toBeVisible()
    },
    pathname: '/staff/admin/customers/14790/expenses',
    screenshotConfig: {},
  },
  '/staff/admin/customers/[customerId]/workspaces': {
    async staffTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Mediaboard devel xx' })).toBeVisible({
        timeout: 10000, // TODO: Clean up playwright+sign_up_... users to speed up customer load
      })

      await expect(
        page.getByText('Workspaces').filter({ has: page.getByText(/\(\d+\)/) }),
      ).toBeVisible()
    },
    pathname: '/staff/admin/customers/14790/workspaces',
    screenshotConfig: {},
  },
  '/staff/admin/users/[userId]': {
    async staffTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Mediaboard devel xx' })).toBeVisible({
        timeout: 10000, // TODO: Clean up playwright+sign_up_... users to speed up customer load
      })
      await expect(page.getByRole('cell', { name: '#13382' })).toBeVisible()
    },
    pathname: '/staff/admin/users/10655',
    screenshotConfig: {},
  },
  '/staff/admin/users/[userId]/daily-access': {
    async staffTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Mediaboard devel xx' })).toBeVisible({
        timeout: 10000, // TODO: Clean up playwright+sign_up_... users to speed up customer load
      })

      await expect(
        page.getByRole('row', { name: '<EMAIL>' }).locator('div').nth(1),
      ).toBeVisible()
    },
    pathname: '/staff/admin/users/10655/daily-access',
    screenshotConfig: {},
  },
  '/staff/admin/workspaces/[workspaceId]': {
    async staffTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Mediaboard devel xx' })).toBeVisible({
        timeout: 10000, // TODO: Clean up playwright+sign_up_... users to speed up customer load
      })
      await expect(page.getByRole('heading', { name: '<EMAIL>' })).toBeVisible()
    },
    pathname: '/staff/admin/workspaces/13382',
    screenshotConfig: {},
  },
  '/staff/admin/workspaces/[workspaceId]/changelog': {
    async staffTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Mediaboard devel xx' })).toBeVisible({
        timeout: 10000, // TODO: Clean up playwright+sign_up_... users to speed up customer load
      })
      await expect(page.getByText('Changed valueMediaboard s.r.o')).toBeVisible()
    },
    pathname: '/staff/admin/workspaces/13382/changelog',
    screenshotConfig: {},
  },
  '/staff/admin/workspaces/[workspaceId]/daily-access': {
    async staffTestBody(page) {
      await expect(page.getByRole('heading', { name: 'Mediaboard devel xx' })).toBeVisible({
        timeout: 10000, // TODO: Clean up playwright+sign_up_... users to speed up customer load
      })

      await expect(
        page.getByRole('row', { name: '<EMAIL>' }).locator('div').nth(1),
      ).toBeVisible()
    },
    pathname: '/staff/admin/workspaces/13382/daily-access',
    screenshotConfig: {},
  },
  '/staff/sign-up': {
    async staffTestBody(page) {
      await expect(page.getByText('Sign Up')).toBeVisible()
    },
    screenshotConfig: {},
  },
  '/staff/sign-up-completion': {
    async staffTestBody(page) {
      await expect(page.getByText('Sign Up', { exact: true })).toBeVisible()
      await expect(page.getByText('Phone')).toBeVisible() // Wait for redirect (on client side)
    },
  },
  '/testing-route': {
    async baseTestBody(page) {
      await expect(page.getByRole('heading', { name: 'AR testing route' })).toBeVisible()
    },
    isAppRouter: true,
  },
  '/topics': {
    async customerDefaultTestBody(page) {
      await expect(page.getByRole('button', { name: 'add New topic' })).toBeVisible()
      await expect(page.getByRole('main').getByRole('img')).toBeHidden({
        // TODO: Fix rendering/perf issue
        // https://github.com/monitora-media/monitora-frontend/issues/3429
        timeout: 10000,
      })
      await expect(page.getByTestId('media_card').first()).toBeVisible()
    },
    async customerPReadOnlyTestBody(page) {
      await expect(page.getByRole('button', { name: 'add New report' })).toBeHidden()
      await expect(page.getByText('Topics (1)')).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerDefault', 'customerHero', 'customerGlobal', 'customerPReadOnly'],
    },
  },
  '/trash': {
    async customerPReadOnlyTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
    screenshotConfig: {
      include: ['customerDefault'],
    },
  },
  '/user/activate': {
    async baseTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
  },
  '/user/yoy-analysis': {
    async baseTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
  },
  '/user/reactivate-24': {
    async baseTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
  },
  '/user/autologin': {
    async baseTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
  },
  '/user/expired': {
    async baseTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
    async customerDefaultTestBody(page, isMobile) {
      if (isMobile) {
        await expect(page.getByRole('button', { name: 'menu' })).toBeVisible()
      } else {
        await expect(page.getByRole('link', { name: 'view_stream Articles' })).toBeVisible()
      }
    },
  },
  '/user/inactive': {
    async baseTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
    async customerDefaultTestBody(page, isMobile) {
      if (isMobile) {
        await expect(page.getByRole('button', { name: 'menu' })).toBeVisible()
      } else {
        await expect(page.getByRole('link', { name: 'view_stream Articles' })).toBeVisible()
      }
    },
  },
  '/user/no-workspace': {
    async baseTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
    async customerDefaultTestBody(page, isMobile) {
      if (isMobile) {
        await expect(page.getByRole('button', { name: 'menu' })).toBeVisible()
      } else {
        await expect(page.getByRole('link', { name: 'view_stream Articles' })).toBeVisible()
      }
    },
  },
  '/user/reset-password': {
    async baseTestBody(page) {
      await expect(page.getByText('Reset Password')).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerDefault'],
    },
  },
  '/user/reset-password/new': {
    async baseTestBody(page) {
      await expect(page).toHaveURL(/\/f404/) // Faux 404
    },
  },
  '/user/reset-password/success': {
    async baseTestBody(page) {
      await expect(page.getByText('We have sent password reset link to your email.')).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerDefault'],
    },
  },
  '/user/settings': {
    async customerDefaultTestBody(page, isMobile) {
      await expect(page.getByText('Tariff information').first()).toBeVisible()
      if (!isMobile) await expect(page.getByText('Tariff information').nth(1)).toBeVisible()
    },
    async customerGlobalTestBody(page, isMobile) {
      await expect(page.getByText('Tariff information').first()).toBeVisible()
      if (!isMobile) await expect(page.getByText('Tariff information').nth(1)).toBeVisible()
      await expect(page.getByText('Agency media').first()).toBeHidden()
      if (!isMobile) await expect(page.getByText('Agency media').nth(1)).toBeHidden()
    },
    async customerPReadOnlyTestBody(page, isMobile) {
      await expect(page.getByText('Tariff information').first()).toBeVisible()
      if (!isMobile) await expect(page.getByText('Tariff information').nth(1)).toBeVisible()
      await expect(page.getByText('Password change').first()).toBeHidden()
      if (!isMobile) await expect(page.getByText('Password change').nth(1)).toBeHidden()
    },
    screenshotConfig: {
      include: ['customerDefault'],
      options: { fullPage: true },
    },
  },
  '/workspace-articles': {
    async customerDefaultTestBody(page) {
      await expect(
        page.getByText("You don't have any articles yet, but you can create one right now."),
      ).toBeVisible()
    },
    screenshotConfig: {
      include: ['customerDefault'],
    },
  },
})

function createPagesFixture<T>(pages: {
  // Enforces `StaffTestType` on `/staff`-prefixed pages
  [K in keyof T]: K extends `/staff${string}` ? StaffTestType : BaseTestType
}) {
  return pages
}

async function expect404(page: Page, pathname: string) {
  const response = await page.request.get(pathname)
  await expect(response.status()).toBe(404)
  await expect(page.getByText('404 - Woop woop woop woop, page not found')).toBeVisible()
}
