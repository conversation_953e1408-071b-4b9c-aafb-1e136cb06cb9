{"name": "pino", "version": "9.7.0", "description": "super fast, all natural json logger", "main": "pino.js", "type": "commonjs", "types": "pino.d.ts", "browser": "./browser.js", "scripts": {"docs": "docsify serve", "browser-test": "airtap --local 8080 test/browser*test.js", "lint": "eslint .", "prepublishOnly": "tap --no-check-coverage test/internals/version.test.js", "test": "npm run lint && npm run transpile && tap --ts && jest test/jest && npm run test-types", "test-ci": "npm run lint && npm run transpile && tap --ts --no-check-coverage --coverage-report=lcovonly && npm run test-types", "test-ci-pnpm": "pnpm run lint && npm run transpile && tap --ts --no-coverage --no-check-coverage && pnpm run test-types", "test-ci-yarn-pnp": "yarn run lint && npm run transpile && tap --ts --no-check-coverage --coverage-report=lcovonly", "test-types": "tsc && tsd && ts-node test/types/pino.ts", "test:smoke": "smoker smoke:pino && smoker smoke:browser && smoker smoke:file", "smoke:pino": "node ./pino.js", "smoke:browser": "node ./browser.js", "smoke:file": "node ./file.js", "transpile": "node ./test/fixtures/ts/transpile.cjs", "cov-ui": "tap --ts --coverage-report=html", "bench": "node benchmarks/utils/runbench all", "bench-basic": "node benchmarks/utils/runbench basic", "bench-object": "node benchmarks/utils/runbench object", "bench-deep-object": "node benchmarks/utils/runbench deep-object", "bench-multi-arg": "node benchmarks/utils/runbench multi-arg", "bench-longs-tring": "node benchmarks/utils/runbench long-string", "bench-child": "node benchmarks/utils/runbench child", "bench-child-child": "node benchmarks/utils/runbench child-child", "bench-child-creation": "node benchmarks/utils/runbench child-creation", "bench-formatters": "node benchmarks/utils/runbench formatters", "update-bench-doc": "node benchmarks/utils/generate-benchmark-doc > docs/benchmarks.md"}, "bin": {"pino": "./bin.js"}, "precommit": "test", "repository": {"type": "git", "url": "git+https://github.com/pinojs/pino.git"}, "keywords": ["fast", "logger", "stream", "json"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://twitter.com/wa7son)"], "license": "MIT", "bugs": {"url": "https://github.com/pinojs/pino/issues"}, "homepage": "https://getpino.io", "devDependencies": {"@types/flush-write-stream": "^1.0.0", "@types/node": "^22.0.0", "@types/tap": "^15.0.6", "@yao-pkg/pkg": "6.3.0", "airtap": "5.0.0", "benchmark": "^2.1.4", "bole": "^5.0.5", "bunyan": "^1.8.14", "debug": "^4.3.4", "docsify-cli": "^4.4.4", "eslint": "^8.17.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "15.7.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.0.0", "execa": "^5.0.0", "fastbench": "^1.0.1", "flush-write-stream": "^2.0.0", "import-fresh": "^3.2.1", "jest": "^29.0.3", "log": "^6.0.0", "loglevel": "^1.6.7", "midnight-smoker": "1.1.1", "pino-pretty": "^11.0.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "pump": "^3.0.0", "rimraf": "^6.0.1", "semver": "^7.3.7", "split2": "^4.0.0", "steed": "^1.1.3", "strip-ansi": "^6.0.0", "tap": "^16.2.0", "tape": "^5.5.3", "through2": "^4.0.0", "ts-node": "^10.9.1", "tsd": "^0.32.0", "typescript": "~5.8.2", "winston": "^3.7.2"}, "dependencies": {"atomic-sleep": "^1.0.0", "fast-redact": "^3.1.1", "on-exit-leak-free": "^2.1.0", "pino-abstract-transport": "^2.0.0", "pino-std-serializers": "^7.0.0", "process-warning": "^5.0.0", "quick-format-unescaped": "^4.0.3", "real-require": "^0.2.0", "safe-stable-stringify": "^2.3.1", "sonic-boom": "^4.0.1", "thread-stream": "^3.0.0"}, "tsd": {"directory": "test/types"}}