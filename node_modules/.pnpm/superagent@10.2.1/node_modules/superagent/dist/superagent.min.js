!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).superagent=t()}}((function(){var t={exports:{}};function e(t){if(t)return function(t){for(var r in e.prototype)t[r]=e.prototype[r];return t}(t)}t.exports=e,e.prototype.on=e.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},e.prototype.once=function(t,e){function r(){this.off(t,r),e.apply(this,arguments)}return r.fn=e,this.on(t,r),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,o=this._callbacks["$"+t];if(!o)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var n=0;n<o.length;n++)if((r=o[n])===e||r.fn===e){o.splice(n,1);break}return 0===o.length&&delete this._callbacks["$"+t],this},e.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),r=this._callbacks["$"+t],o=1;o<arguments.length;o++)e[o-1]=arguments[o];if(r){o=0;for(var n=(r=r.slice(0)).length;o<n;++o)r[o].apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},e.prototype.hasListeners=function(t){return!!this.listeners(t).length},t=t.exports;var r;r=a,a.default=a,a.stable=u,a.stableStringify=u;var o=[],n=[];function i(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function a(t,e,r,a){var p;void 0===a&&(a=i()),function t(e,r,o,n,i,a,p){var u;if(a+=1,"object"==typeof e&&null!==e){for(u=0;u<n.length;u++)if(n[u]===e)return void s("[Circular]",e,r,i);if(void 0!==p.depthLimit&&a>p.depthLimit)return void s("[...]",e,r,i);if(void 0!==p.edgesLimit&&o+1>p.edgesLimit)return void s("[...]",e,r,i);if(n.push(e),Array.isArray(e))for(u=0;u<e.length;u++)t(e[u],u,u,n,e,a,p);else{var l=Object.keys(e);for(u=0;u<l.length;u++){var c=l[u];t(e[c],c,u,n,e,a,p)}}n.pop()}}(t,"",0,[],void 0,0,a);try{p=0===n.length?JSON.stringify(t,e,r):JSON.stringify(t,l(e),r)}catch(c){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==o.length;){var u=o.pop();4===u.length?Object.defineProperty(u[0],u[1],u[3]):u[0][u[1]]=u[2]}}return p}function s(t,e,r,i){var a=Object.getOwnPropertyDescriptor(i,r);void 0!==a.get?a.configurable?(Object.defineProperty(i,r,{value:t}),o.push([i,r,e,a])):n.push([e,r,t]):(i[r]=t,o.push([i,r,e]))}function p(t,e){return t<e?-1:t>e?1:0}function u(t,e,r,a){void 0===a&&(a=i());var u,c=function t(e,r,n,i,a,u,l){var c;if(u+=1,"object"==typeof e&&null!==e){for(c=0;c<i.length;c++)if(i[c]===e)return void s("[Circular]",e,r,a);try{if("function"==typeof e.toJSON)return}catch(d){return}if(void 0!==l.depthLimit&&u>l.depthLimit)return void s("[...]",e,r,a);if(void 0!==l.edgesLimit&&n+1>l.edgesLimit)return void s("[...]",e,r,a);if(i.push(e),Array.isArray(e))for(c=0;c<e.length;c++)t(e[c],c,c,i,e,u,l);else{var f={},y=Object.keys(e).sort(p);for(c=0;c<y.length;c++){var h=y[c];t(e[h],h,c,i,e,u,l),f[h]=e[h]}if(void 0===a)return f;o.push([a,r,e]),a[r]=f}i.pop()}}(t,"",0,[],void 0,0,a)||t;try{u=0===n.length?JSON.stringify(c,e,r):JSON.stringify(c,l(e),r)}catch(y){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==o.length;){var f=o.pop();4===f.length?Object.defineProperty(f[0],f[1],f[3]):f[0][f[1]]=f[2]}}return u}function l(t){return t=void 0!==t?t:function(t,e){return e},function(e,r){if(n.length>0)for(var o=0;o<n.length;o++){var i=n[o];if(i[1]===e&&i[0]===r){r=i[2],n.splice(o,1);break}}return t.call(this,e,r)}}var c=TypeError,f={},y={};(function(t){(function(){var e="function"==typeof Map&&Map.prototype,r=Object.getOwnPropertyDescriptor&&e?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,o=e&&r&&"function"==typeof r.get?r.get:null,n=e&&Map.prototype.forEach,i="function"==typeof Set&&Set.prototype,a=Object.getOwnPropertyDescriptor&&i?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,s=i&&a&&"function"==typeof a.get?a.get:null,p=i&&Set.prototype.forEach,u="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,l="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,c="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,h=Boolean.prototype.valueOf,d=Object.prototype.toString,m=Function.prototype.toString,g=String.prototype.match,b=String.prototype.slice,v=String.prototype.replace,w=String.prototype.toUpperCase,_=String.prototype.toLowerCase,S=RegExp.prototype.test,A=Array.prototype.concat,E=Array.prototype.join,O=Array.prototype.slice,j=Math.floor,T="function"==typeof BigInt?BigInt.prototype.valueOf:null,P=Object.getOwnPropertySymbols,x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,R="function"==typeof Symbol&&"object"==typeof Symbol.iterator,k="function"==typeof Symbol&&Symbol.toStringTag&&(Symbol.toStringTag,1)?Symbol.toStringTag:null,I=Object.prototype.propertyIsEnumerable,D=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(t){return t.__proto__}:null);function C(t,e){if(t===1/0||t===-1/0||t!=t||t&&t>-1e3&&t<1e3||S.call(/e/,e))return e;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof t){var o=t<0?-j(-t):j(t);if(o!==t){var n=String(o),i=b.call(e,n.length+1);return v.call(n,r,"$&_")+"."+v.call(v.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return v.call(e,r,"$&_")}var F=f.custom,N=$(F)?F:null,M={__proto__:null,double:'"',single:"'"},U={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};function q(t,e,r){var o=r.quoteStyle||e,n=M[o];return n+t+n}function L(t){return v.call(String(t),/"/g,"&quot;")}function B(t){return!k||!("object"==typeof t&&(k in t||void 0!==t[k]))}function W(t){return"[object Array]"===J(t)&&B(t)}function H(t){return"[object RegExp]"===J(t)&&B(t)}function $(t){if(R)return t&&"object"==typeof t&&t instanceof Symbol;if("symbol"==typeof t)return!0;if(!t||"object"!=typeof t||!x)return!1;try{return x.call(t),!0}catch(e){}return!1}y=function e(r,i,a,y){var d=i||{};if(G(d,"quoteStyle")&&!G(M,d.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(G(d,"maxStringLength")&&("number"==typeof d.maxStringLength?d.maxStringLength<0&&d.maxStringLength!==1/0:null!==d.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var w=!G(d,"customInspect")||d.customInspect;if("boolean"!=typeof w&&"symbol"!==w)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(G(d,"indent")&&null!==d.indent&&"\t"!==d.indent&&!(parseInt(d.indent,10)===d.indent&&d.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(G(d,"numericSeparator")&&"boolean"!=typeof d.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var S=d.numericSeparator;if(void 0===r)return"undefined";if(null===r)return"null";if("boolean"==typeof r)return r?"true":"false";if("string"==typeof r)return function t(e,r){if(e.length>r.maxStringLength){var o=e.length-r.maxStringLength,n="... "+o+" more character"+(o>1?"s":"");return t(b.call(e,0,r.maxStringLength),r)+n}var i=U[r.quoteStyle||"single"];return i.lastIndex=0,q(v.call(v.call(e,i,"\\$1"),/[\x00-\x1f]/g,V),"single",r)}(r,d);if("number"==typeof r){if(0===r)return 1/0/r>0?"0":"-0";var j=String(r);return S?C(r,j):j}if("bigint"==typeof r){var P=String(r)+"n";return S?C(r,P):P}var F=void 0===d.depth?5:d.depth;if(void 0===a&&(a=0),a>=F&&F>0&&"object"==typeof r)return W(r)?"[Array]":"[Object]";var z,et=function(t,e){var r;if("\t"===t.indent)r="\t";else{if(!("number"==typeof t.indent&&t.indent>0))return null;r=E.call(Array(t.indent+1)," ")}return{base:r,prev:E.call(Array(e+1),r)}}(d,a);if(void 0===y)y=[];else if(K(y,r)>=0)return"[Circular]";function rt(t,r,o){if(r&&(y=O.call(y)).push(r),o){var n={depth:d.depth};return G(d,"quoteStyle")&&(n.quoteStyle=d.quoteStyle),e(t,n,a+1,y)}return e(t,d,a+1,y)}if("function"==typeof r&&!H(r)){var ot=function(t){if(t.name)return t.name;var e=g.call(m.call(t),/^function\s*([\w$]+)/);return e?e[1]:null}(r),nt=tt(r,rt);return"[Function"+(ot?": "+ot:" (anonymous)")+"]"+(nt.length>0?" { "+E.call(nt,", ")+" }":"")}if($(r)){var it=R?v.call(String(r),/^(Symbol\(.*\))_[^)]*$/,"$1"):x.call(r);return"object"!=typeof r||R?it:Q(it)}if((z=r)&&"object"==typeof z&&("undefined"!=typeof HTMLElement&&z instanceof HTMLElement||"string"==typeof z.nodeName&&"function"==typeof z.getAttribute)){for(var at="<"+_.call(String(r.nodeName)),st=r.attributes||[],pt=0;pt<st.length;pt++)at+=" "+st[pt].name+"="+q(L(st[pt].value),"double",d);return at+=">",r.childNodes&&r.childNodes.length&&(at+="..."),at+"</"+_.call(String(r.nodeName))+">"}if(W(r)){if(0===r.length)return"[]";var ut=tt(r,rt);return et&&!function(t){for(var e=0;e<t.length;e++)if(K(t[e],"\n")>=0)return!1;return!0}(ut)?"["+Z(ut,et)+"]":"[ "+E.call(ut,", ")+" ]"}if(function(t){return"[object Error]"===J(t)&&B(t)}(r)){var lt=tt(r,rt);return"cause"in Error.prototype||!("cause"in r)||I.call(r,"cause")?0===lt.length?"["+String(r)+"]":"{ ["+String(r)+"] "+E.call(lt,", ")+" }":"{ ["+String(r)+"] "+E.call(A.call("[cause]: "+rt(r.cause),lt),", ")+" }"}if("object"==typeof r&&w){if(N&&"function"==typeof r[N]&&f)return f(r,{depth:F-a});if("symbol"!==w&&"function"==typeof r.inspect)return r.inspect()}if(function(t){if(!o||!t||"object"!=typeof t)return!1;try{o.call(t);try{s.call(t)}catch(at){return!0}return t instanceof Map}catch(e){}return!1}(r)){var ct=[];return n&&n.call(r,(function(t,e){ct.push(rt(e,r,!0)+" => "+rt(t,r))})),Y("Map",o.call(r),ct,et)}if(function(t){if(!s||!t||"object"!=typeof t)return!1;try{s.call(t);try{o.call(t)}catch(e){return!0}return t instanceof Set}catch(r){}return!1}(r)){var ft=[];return p&&p.call(r,(function(t){ft.push(rt(t,r))})),Y("Set",s.call(r),ft,et)}if(function(t){if(!u||!t||"object"!=typeof t)return!1;try{u.call(t,u);try{l.call(t,l)}catch(at){return!0}return t instanceof WeakMap}catch(e){}return!1}(r))return X("WeakMap");if(function(t){if(!l||!t||"object"!=typeof t)return!1;try{l.call(t,l);try{u.call(t,u)}catch(at){return!0}return t instanceof WeakSet}catch(e){}return!1}(r))return X("WeakSet");if(function(t){if(!c||!t||"object"!=typeof t)return!1;try{return c.call(t),!0}catch(e){}return!1}(r))return X("WeakRef");if(function(t){return"[object Number]"===J(t)&&B(t)}(r))return Q(rt(Number(r)));if(function(t){if(!t||"object"!=typeof t||!T)return!1;try{return T.call(t),!0}catch(e){}return!1}(r))return Q(rt(T.call(r)));if(function(t){return"[object Boolean]"===J(t)&&B(t)}(r))return Q(h.call(r));if(function(t){return"[object String]"===J(t)&&B(t)}(r))return Q(rt(String(r)));if("undefined"!=typeof window&&r===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&r===globalThis||void 0!==t&&r===t)return"{ [object globalThis] }";if(!function(t){return"[object Date]"===J(t)&&B(t)}(r)&&!H(r)){var yt=tt(r,rt),ht=D?D(r)===Object.prototype:r instanceof Object||r.constructor===Object,dt=r instanceof Object?"":"null prototype",mt=!ht&&k&&Object(r)===r&&k in r?b.call(J(r),8,-1):dt?"Object":"",gt=(ht||"function"!=typeof r.constructor?"":r.constructor.name?r.constructor.name+" ":"")+(mt||dt?"["+E.call(A.call([],mt||[],dt||[]),": ")+"] ":"");return 0===yt.length?gt+"{}":et?gt+"{"+Z(yt,et)+"}":gt+"{ "+E.call(yt,", ")+" }"}return String(r)};var z=Object.prototype.hasOwnProperty||function(t){return t in this};function G(t,e){return z.call(t,e)}function J(t){return d.call(t)}function K(t,e){if(t.indexOf)return t.indexOf(e);for(var r=0,o=t.length;r<o;r++)if(t[r]===e)return r;return-1}function V(t){var e=t.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[e];return r?"\\"+r:"\\x"+(e<16?"0":"")+w.call(e.toString(16))}function Q(t){return"Object("+t+")"}function X(t){return t+" { ? }"}function Y(t,e,r,o){return t+" ("+e+") {"+(o?Z(r,o):E.call(r,", "))+"}"}function Z(t,e){if(0===t.length)return"";var r="\n"+e.prev+e.base;return r+E.call(t,","+r)+"\n"+e.prev}function tt(t,e){var r=W(t),o=[];if(r){o.length=t.length;for(var n=0;n<t.length;n++)o[n]=G(t,n)?e(t[n],t):""}var i,a="function"==typeof P?P(t):[];if(R){i={};for(var s=0;s<a.length;s++)i["$"+a[s]]=a[s]}for(var p in t)G(t,p)&&(r&&String(Number(p))===p&&p<t.length||R&&i["$"+p]instanceof Symbol||(S.call(/[^\w$]/,p)?o.push(e(p,t)+": "+e(t[p],t)):o.push(p+": "+e(t[p],t))));if("function"==typeof P)for(var u=0;u<a.length;u++)I.call(t,a[u])&&o.push("["+e(a[u])+"]: "+e(t[a[u]],t));return o}}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});var h=function(t,e,r){for(var o,n=t;null!=(o=n.next);n=o)if(o.key===e)return n.next=o.next,r||(o.next=t.next,t.next=o),o},d=Object,m=Error,g=EvalError,b=RangeError,v=ReferenceError,w=SyntaxError,_=URIError,S=Math.abs,A=Math.floor,E=Math.max,O=Math.min,j=Math.pow,T=Math.round,P=Number.isNaN||function(t){return t!=t},x=Object.getOwnPropertyDescriptor;if(x)try{x([],"length")}catch(Oe){x=null}var R=x,k=Object.defineProperty||!1;if(k)try{k({},"a",{value:1})}catch(Oe){k=!1}var I,D=k,C="undefined"!=typeof Symbol&&Symbol,F="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null,N=d.getPrototypeOf||null,M=Object.prototype.toString,U=Math.max,q=function(t,e){for(var r=[],o=0;o<t.length;o+=1)r[o]=t[o];for(var n=0;n<e.length;n+=1)r[n+t.length]=e[n];return r},L=Function.prototype.bind||function(t){var e=this;if("function"!=typeof e||"[object Function]"!==M.apply(e))throw new TypeError("Function.prototype.bind called on incompatible "+e);for(var r,o=function(t,e){for(var r=[],o=1,n=0;o<t.length;o+=1,n+=1)r[n]=t[o];return r}(arguments),n=U(0,e.length-o.length),i=[],a=0;a<n;a++)i[a]="$"+a;if(r=Function("binder","return function ("+function(t,e){for(var r="",o=0;o<t.length;o+=1)r+=t[o],o+1<t.length&&(r+=",");return r}(i)+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof r){var n=e.apply(this,q(o,arguments));return Object(n)===n?n:this}return e.apply(t,q(o,arguments))})),e.prototype){var s=function(){};s.prototype=e.prototype,r.prototype=new s,s.prototype=null}return r},B=Function.prototype.call,W=Function.prototype.apply,H="undefined"!=typeof Reflect&&Reflect&&Reflect.apply||L.call(B,W),$=function(t){if(t.length<1||"function"!=typeof t[0])throw new c("a function is required");return H(L,B,t)};try{I=[].__proto__===Array.prototype}catch(Oe){if(!Oe||"object"!=typeof Oe||!("code"in Oe)||"ERR_PROTO_ACCESS"!==Oe.code)throw Oe}var z=!!I&&R&&R(Object.prototype,"__proto__"),G=Object,J=G.getPrototypeOf,K=z&&"function"==typeof z.get?$([z.get]):"function"==typeof J&&function(t){return J(null==t?t:G(t))},V=F?function(t){return F(t)}:N?function(t){if(!t||"object"!=typeof t&&"function"!=typeof t)throw new TypeError("getProto: not an object");return N(t)}:K?function(t){return K(t)}:null,Q=Function.prototype.call,X=Object.prototype.hasOwnProperty,Y=L.call(Q,X),Z=Function,tt=function(t){try{return Z('"use strict"; return ('+t+").constructor;")()}catch(Oe){}},et=function(){throw new c},rt=R?function(){try{return et}catch(t){try{return R(arguments,"callee").get}catch(e){return et}}}():et,ot="function"==typeof C&&"function"==typeof Symbol&&"symbol"==typeof C("foo")&&"symbol"==typeof Symbol("bar")&&function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),r=Object(e);if("string"==typeof e)return!1;if("[object Symbol]"!==Object.prototype.toString.call(e))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(var o in t[e]=42,t)return!1;if("function"==typeof Object.keys&&0!==Object.keys(t).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var n=Object.getOwnPropertySymbols(t);if(1!==n.length||n[0]!==e)return!1;if(!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(t,e);if(42!==i.value||!0!==i.enumerable)return!1}return!0}(),nt={},it="undefined"!=typeof Uint8Array&&V?V(Uint8Array):void 0,at={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?void 0:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?void 0:ArrayBuffer,"%ArrayIteratorPrototype%":ot&&V?V([][Symbol.iterator]()):void 0,"%AsyncFromSyncIteratorPrototype%":void 0,"%AsyncFunction%":nt,"%AsyncGenerator%":nt,"%AsyncGeneratorFunction%":nt,"%AsyncIteratorPrototype%":nt,"%Atomics%":"undefined"==typeof Atomics?void 0:Atomics,"%BigInt%":"undefined"==typeof BigInt?void 0:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?void 0:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?void 0:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?void 0:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":m,"%eval%":eval,"%EvalError%":g,"%Float16Array%":"undefined"==typeof Float16Array?void 0:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?void 0:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?void 0:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?void 0:FinalizationRegistry,"%Function%":Z,"%GeneratorFunction%":nt,"%Int8Array%":"undefined"==typeof Int8Array?void 0:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?void 0:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?void 0:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":ot&&V?V(V([][Symbol.iterator]())):void 0,"%JSON%":"object"==typeof JSON?JSON:void 0,"%Map%":"undefined"==typeof Map?void 0:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&ot&&V?V((new Map)[Symbol.iterator]()):void 0,"%Math%":Math,"%Number%":Number,"%Object%":d,"%Object.getOwnPropertyDescriptor%":R,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?void 0:Promise,"%Proxy%":"undefined"==typeof Proxy?void 0:Proxy,"%RangeError%":b,"%ReferenceError%":v,"%Reflect%":"undefined"==typeof Reflect?void 0:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?void 0:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&ot&&V?V((new Set)[Symbol.iterator]()):void 0,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?void 0:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":ot&&V?V(""[Symbol.iterator]()):void 0,"%Symbol%":ot?Symbol:void 0,"%SyntaxError%":w,"%ThrowTypeError%":rt,"%TypedArray%":it,"%TypeError%":c,"%Uint8Array%":"undefined"==typeof Uint8Array?void 0:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?void 0:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?void 0:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?void 0:Uint32Array,"%URIError%":_,"%WeakMap%":"undefined"==typeof WeakMap?void 0:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?void 0:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?void 0:WeakSet,"%Function.prototype.call%":B,"%Function.prototype.apply%":W,"%Object.defineProperty%":D,"%Object.getPrototypeOf%":N,"%Math.abs%":S,"%Math.floor%":A,"%Math.max%":E,"%Math.min%":O,"%Math.pow%":j,"%Math.round%":T,"%Math.sign%":function(t){return P(t)||0===t?t:t<0?-1:1},"%Reflect.getPrototypeOf%":F};if(V)try{null.error}catch(Oe){var st=V(V(Oe));at["%Error.prototype%"]=st}var pt={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},ut=L.call(B,Array.prototype.concat),lt=L.call(W,Array.prototype.splice),ct=L.call(B,String.prototype.replace),ft=L.call(B,String.prototype.slice),yt=L.call(B,RegExp.prototype.exec),ht=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,dt=/\\(\\)?/g,mt=function(t,e){var r,o=t;if(Y(pt,o)&&(o="%"+(r=pt[o])[0]+"%"),Y(at,o)){var n=at[o];if(n===nt&&(n=function t(e){var r;if("%AsyncFunction%"===e)r=tt("async function () {}");else if("%GeneratorFunction%"===e)r=tt("function* () {}");else if("%AsyncGeneratorFunction%"===e)r=tt("async function* () {}");else if("%AsyncGenerator%"===e){var o=t("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===e){var n=t("%AsyncGenerator%");n&&V&&(r=V(n.prototype))}return at[e]=r,r}(o)),void 0===n&&!e)throw new c("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new w("intrinsic "+t+" does not exist!")},gt=function(t,e){if("string"!=typeof t||0===t.length)throw new c("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof e)throw new c('"allowMissing" argument must be a boolean');if(null===yt(/^%?[^%]*%?$/,t))throw new w("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(t){var e=ft(t,0,1),r=ft(t,-1);if("%"===e&&"%"!==r)throw new w("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==e)throw new w("invalid intrinsic syntax, expected opening `%`");var o=[];return ct(t,ht,(function(t,e,r,n){o[o.length]=r?ct(n,dt,"$1"):e||t})),o}(t),o=r.length>0?r[0]:"",n=mt("%"+o+"%",e),i=n.name,a=n.value,s=!1,p=n.alias;p&&(o=p[0],lt(r,ut([0,1],p)));for(var u=1,l=!0;u<r.length;u+=1){var f=r[u],y=ft(f,0,1),h=ft(f,-1);if(('"'===y||"'"===y||"`"===y||'"'===h||"'"===h||"`"===h)&&y!==h)throw new w("property names with quotes must have matching quotes");if("constructor"!==f&&l||(s=!0),Y(at,i="%"+(o+="."+f)+"%"))a=at[i];else if(null!=a){if(!(f in a)){if(!e)throw new c("base intrinsic for "+t+" exists, but the property is not available.");return}if(R&&u+1>=r.length){var d=R(a,f);a=(l=!!d)&&"get"in d&&!("originalValue"in d.get)?d.get:a[f]}else l=Y(a,f),a=a[f];l&&!s&&(at[i]=a)}}return a},bt=$([gt("%String.prototype.indexOf%")]),vt=function(t,e){var r=gt(t,!!e);return"function"==typeof r&&bt(t,".prototype.")>-1?$([r]):r},wt=gt("%Map%",!0),_t=vt("Map.prototype.get",!0),St=vt("Map.prototype.set",!0),At=vt("Map.prototype.has",!0),Et=vt("Map.prototype.delete",!0),Ot=vt("Map.prototype.size",!0),jt=!!wt&&function(){var t,e={assert:function(t){if(!e.has(t))throw new c("Side channel does not contain "+y(t))},delete:function(e){if(t){var r=Et(t,e);return 0===Ot(t)&&(t=void 0),r}return!1},get:function(e){if(t)return _t(t,e)},has:function(e){return!!t&&At(t,e)},set:function(e,r){t||(t=new wt),St(t,e,r)}};return e},Tt=gt("%WeakMap%",!0),Pt=vt("WeakMap.prototype.get",!0),xt=vt("WeakMap.prototype.set",!0),Rt=vt("WeakMap.prototype.has",!0),kt=vt("WeakMap.prototype.delete",!0),It=(Tt?function(){var t,e,r={assert:function(t){if(!r.has(t))throw new c("Side channel does not contain "+y(t))},delete:function(r){if(Tt&&r&&("object"==typeof r||"function"==typeof r)){if(t)return kt(t,r)}else if(jt&&e)return e.delete(r);return!1},get:function(r){return Tt&&r&&("object"==typeof r||"function"==typeof r)&&t?Pt(t,r):e&&e.get(r)},has:function(r){return Tt&&r&&("object"==typeof r||"function"==typeof r)&&t?Rt(t,r):!!e&&e.has(r)},set:function(r,o){Tt&&r&&("object"==typeof r||"function"==typeof r)?(t||(t=new Tt),xt(t,r,o)):jt&&(e||(e=jt()),e.set(r,o))}};return r}:jt)||jt||function(){var t,e={assert:function(t){if(!e.has(t))throw new c("Side channel does not contain "+y(t))},delete:function(e){var r=t&&t.next,o=function(t,e){if(t)return h(t,e,!0)}(t,e);return o&&r&&r===o&&(t=void 0),!!o},get:function(e){return function(t,e){if(t){var r=h(t,e);return r&&r.value}}(t,e)},has:function(e){return function(t,e){return!!t&&!!h(t,e)}(t,e)},set:function(e,r){t||(t={next:void 0}),function(t,e,r){var o=h(t,e);o?o.value=r:t.next={key:e,next:t.next,value:r}}(t,e,r)}};return e},Dt=function(){var t,e={assert:function(t){if(!e.has(t))throw new c("Side channel does not contain "+y(t))},delete:function(e){return!!t&&t.delete(e)},get:function(e){return t&&t.get(e)},has:function(e){return!!t&&t.has(e)},set:function(e,r){t||(t=It()),t.set(e,r)}};return e},Ct=String.prototype.replace,Ft=/%20/g,Nt={default:"RFC3986",formatters:{RFC1738:function(t){return Ct.call(t,Ft,"+")},RFC3986:function(t){return String(t)}},RFC1738:"RFC1738",RFC3986:"RFC3986"},Mt=Object.prototype.hasOwnProperty,Ut=Array.isArray,qt=function(){for(var t=[],e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}(),Lt={combine:function(t,e){return[].concat(t,e)},compact:function(t){for(var e=[{obj:{o:t},prop:"o"}],r=[],o=0;o<e.length;++o)for(var n=e[o],i=n.obj[n.prop],a=Object.keys(i),s=0;s<a.length;++s){var p=a[s],u=i[p];"object"==typeof u&&null!==u&&-1===r.indexOf(u)&&(e.push({obj:i,prop:p}),r.push(u))}return function(t){for(;t.length>1;){var e=t.pop(),r=e.obj[e.prop];if(Ut(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);e.obj[e.prop]=o}}}(e),t},decode:function(t,e,r){var o=t.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(Oe){return o}},encode:function(t,e,r,o,n){if(0===t.length)return t;var i=t;if("symbol"==typeof t?i=Symbol.prototype.toString.call(t):"string"!=typeof t&&(i=String(t)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,(function(t){return"%26%23"+parseInt(t.slice(2),16)+"%3B"}));for(var a="",s=0;s<i.length;s+=1024){for(var p=i.length>=1024?i.slice(s,s+1024):i,u=[],l=0;l<p.length;++l){var c=p.charCodeAt(l);45===c||46===c||95===c||126===c||c>=48&&c<=57||c>=65&&c<=90||c>=97&&c<=122||n===Nt.RFC1738&&(40===c||41===c)?u[u.length]=p.charAt(l):c<128?u[u.length]=qt[c]:c<2048?u[u.length]=qt[192|c>>6]+qt[128|63&c]:c<55296||c>=57344?u[u.length]=qt[224|c>>12]+qt[128|c>>6&63]+qt[128|63&c]:(l+=1,c=65536+((1023&c)<<10|1023&p.charCodeAt(l)),u[u.length]=qt[240|c>>18]+qt[128|c>>12&63]+qt[128|c>>6&63]+qt[128|63&c])}a+=u.join("")}return a},isBuffer:function(t){return!(!t||"object"!=typeof t||!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t)))},isRegExp:function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},maybeMap:function(t,e){if(Ut(t)){for(var r=[],o=0;o<t.length;o+=1)r.push(e(t[o]));return r}return e(t)},merge:function t(e,r,o){if(!r)return e;if("object"!=typeof r&&"function"!=typeof r){if(Ut(e))e.push(r);else{if(!e||"object"!=typeof e)return[e,r];(o&&(o.plainObjects||o.allowPrototypes)||!Mt.call(Object.prototype,r))&&(e[r]=!0)}return e}if(!e||"object"!=typeof e)return[e].concat(r);var n=e;return Ut(e)&&!Ut(r)&&(n=function(t,e){for(var r=e&&e.plainObjects?{__proto__:null}:{},o=0;o<t.length;++o)void 0!==t[o]&&(r[o]=t[o]);return r}(e,o)),Ut(e)&&Ut(r)?(r.forEach((function(r,n){if(Mt.call(e,n)){var i=e[n];i&&"object"==typeof i&&r&&"object"==typeof r?e[n]=t(i,r,o):e.push(r)}else e[n]=r})),e):Object.keys(r).reduce((function(e,n){var i=r[n];return Mt.call(e,n)?e[n]=t(e[n],i,o):e[n]=i,e}),n)}},Bt=Object.prototype.hasOwnProperty,Wt={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,e){return t+"["+e+"]"},repeat:function(t){return t}},Ht=Array.isArray,$t=Array.prototype.push,zt=function(t,e){$t.apply(t,Ht(e)?e:[e])},Gt=Date.prototype.toISOString,Jt=Nt.default,Kt={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:Lt.encode,encodeValuesOnly:!1,filter:void 0,format:Jt,formatter:Nt.formatters[Jt],indices:!1,serializeDate:function(t){return Gt.call(t)},skipNulls:!1,strictNullHandling:!1},Vt={},Qt=function t(e,r,o,n,i,a,s,p,u,l,c,f,y,h,d,m,g,b){for(var v,w=e,_=b,S=0,A=!1;void 0!==(_=_.get(Vt))&&!A;){var E=_.get(e);if(S+=1,void 0!==E){if(E===S)throw new RangeError("Cyclic object value");A=!0}void 0===_.get(Vt)&&(S=0)}if("function"==typeof l?w=l(r,w):w instanceof Date?w=y(w):"comma"===o&&Ht(w)&&(w=Lt.maybeMap(w,(function(t){return t instanceof Date?y(t):t}))),null===w){if(a)return u&&!m?u(r,Kt.encoder,g,"key",h):r;w=""}if("string"==typeof(v=w)||"number"==typeof v||"boolean"==typeof v||"symbol"==typeof v||"bigint"==typeof v||Lt.isBuffer(w))return u?[d(m?r:u(r,Kt.encoder,g,"key",h))+"="+d(u(w,Kt.encoder,g,"value",h))]:[d(r)+"="+d(String(w))];var O,j=[];if(void 0===w)return j;if("comma"===o&&Ht(w))m&&u&&(w=Lt.maybeMap(w,u)),O=[{value:w.length>0?w.join(",")||null:void 0}];else if(Ht(l))O=l;else{var T=Object.keys(w);O=c?T.sort(c):T}var P=p?String(r).replace(/\./g,"%2E"):String(r),x=n&&Ht(w)&&1===w.length?P+"[]":P;if(i&&Ht(w)&&0===w.length)return x+"[]";for(var R=0;R<O.length;++R){var k=O[R],I="object"==typeof k&&k&&void 0!==k.value?k.value:w[k];if(!s||null!==I){var D=f&&p?String(k).replace(/\./g,"%2E"):String(k),C=Ht(w)?"function"==typeof o?o(x,D):x:x+(f?"."+D:"["+D+"]");b.set(e,S);var F=Dt();F.set(Vt,b),zt(j,t(I,C,o,n,i,a,s,p,"comma"===o&&m&&Ht(w)?null:u,l,c,f,y,h,d,m,g,F))}}return j},Xt=(Object.prototype.hasOwnProperty,Array.isArray,function(t,e){var r,o=t,n=function(t){if(!t)return Kt;if(void 0!==t.allowEmptyArrays&&"boolean"!=typeof t.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==t.encodeDotInKeys&&"boolean"!=typeof t.encodeDotInKeys)throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==t.encoder&&void 0!==t.encoder&&"function"!=typeof t.encoder)throw new TypeError("Encoder has to be a function.");var e=t.charset||Kt.charset;if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=Nt.default;if(void 0!==t.format){if(!Bt.call(Nt.formatters,t.format))throw new TypeError("Unknown format option provided.");r=t.format}var o,n=Nt.formatters[r],i=Kt.filter;if(("function"==typeof t.filter||Ht(t.filter))&&(i=t.filter),o=t.arrayFormat in Wt?t.arrayFormat:"indices"in t?t.indices?"indices":"repeat":Kt.arrayFormat,"commaRoundTrip"in t&&"boolean"!=typeof t.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var a=void 0===t.allowDots?!0===t.encodeDotInKeys||Kt.allowDots:!!t.allowDots;return{addQueryPrefix:"boolean"==typeof t.addQueryPrefix?t.addQueryPrefix:Kt.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof t.allowEmptyArrays?!!t.allowEmptyArrays:Kt.allowEmptyArrays,arrayFormat:o,charset:e,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:Kt.charsetSentinel,commaRoundTrip:!!t.commaRoundTrip,delimiter:void 0===t.delimiter?Kt.delimiter:t.delimiter,encode:"boolean"==typeof t.encode?t.encode:Kt.encode,encodeDotInKeys:"boolean"==typeof t.encodeDotInKeys?t.encodeDotInKeys:Kt.encodeDotInKeys,encoder:"function"==typeof t.encoder?t.encoder:Kt.encoder,encodeValuesOnly:"boolean"==typeof t.encodeValuesOnly?t.encodeValuesOnly:Kt.encodeValuesOnly,filter:i,format:r,formatter:n,serializeDate:"function"==typeof t.serializeDate?t.serializeDate:Kt.serializeDate,skipNulls:"boolean"==typeof t.skipNulls?t.skipNulls:Kt.skipNulls,sort:"function"==typeof t.sort?t.sort:null,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:Kt.strictNullHandling}}(e);"function"==typeof n.filter?o=(0,n.filter)("",o):Ht(n.filter)&&(r=n.filter);var i=[];if("object"!=typeof o||null===o)return"";var a=Wt[n.arrayFormat],s="comma"===a&&n.commaRoundTrip;r||(r=Object.keys(o)),n.sort&&r.sort(n.sort);for(var p=Dt(),u=0;u<r.length;++u){var l=r[u],c=o[l];n.skipNulls&&null===c||zt(i,Qt(c,l,a,s,n.allowEmptyArrays,n.strictNullHandling,n.skipNulls,n.encodeDotInKeys,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset,p))}var f=i.join(n.delimiter),y=!0===n.addQueryPrefix?"?":"";return n.charsetSentinel&&("iso-8859-1"===n.charset?y+="utf8=%26%2310003%3B&":y+="utf8=%E2%9C%93&"),f.length>0?y+f:""}),Yt={type:t=>t.split(/ *; */).shift(),params:t=>{const e={};for(const r of t.split(/ *; */)){const t=r.split(/ *= */),o=t.shift(),n=t.shift();o&&n&&(e[o]=n)}return e},parseLinks:t=>{const e={};for(const r of t.split(/ *, */)){const t=r.split(/ *; */),o=t[0].slice(1,-1);e[t[1].split(/ *= */)[1].slice(1,-1)]=o}return e},isObject:t=>null!==t&&"object"==typeof t};Yt.hasOwn=Object.hasOwn||function(t,e){if(null==t)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(new Object(t),e)},Yt.mixin=(t,e)=>{for(const r in e)Yt.hasOwn(e,r)&&(t[r]=e[r])};var Zt;const{isObject:te,hasOwn:ee}=Yt;function re(){}Zt=re,re.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),clearTimeout(this._uploadTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,delete this._uploadTimeoutTimer,this},re.prototype.parse=function(t){return this._parser=t,this},re.prototype.responseType=function(t){return this._responseType=t,this},re.prototype.serialize=function(t){return this._serializer=t,this},re.prototype.timeout=function(t){if(!t||"object"!=typeof t)return this._timeout=t,this._responseTimeout=0,this._uploadTimeout=0,this;for(const e in t)if(ee(t,e))switch(e){case"deadline":this._timeout=t.deadline;break;case"response":this._responseTimeout=t.response;break;case"upload":this._uploadTimeout=t.upload;break;default:console.warn("Unknown timeout option",e)}return this},re.prototype.retry=function(t,e){return 0!==arguments.length&&!0!==t||(t=1),t<=0&&(t=0),this._maxRetries=t,this._retries=0,this._retryCallback=e,this};const oe=new Set(["ETIMEDOUT","ECONNRESET","EADDRINUSE","ECONNREFUSED","EPIPE","ENOTFOUND","ENETUNREACH","EAI_AGAIN"]),ne=new Set([408,413,429,500,502,503,504,521,522,524]);re.prototype._shouldRetry=function(t,e){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{const r=this._retryCallback(t,e);if(!0===r)return!0;if(!1===r)return!1}catch(r){console.error(r)}if(e&&e.status&&ne.has(e.status))return!0;if(t){if(t.code&&oe.has(t.code))return!0;if(t.timeout&&"ECONNABORTED"===t.code)return!0;if(t.crossDomain)return!0}return!1},re.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this.timedoutError=null,this._end()},re.prototype.then=function(t,e){if(!this._fullfilledPromise){const t=this;this._endCalled&&console.warn("Warning: superagent request was sent twice, because both .end() and .then() were called. Never call .end() if you use promises"),this._fullfilledPromise=new Promise((e,r)=>{t.on("abort",()=>{if(this._maxRetries&&this._maxRetries>this._retries)return;if(this.timedout&&this.timedoutError)return void r(this.timedoutError);const t=new Error("Aborted");t.code="ABORTED",t.status=this.status,t.method=this.method,t.url=this.url,r(t)}),t.end((t,o)=>{t?r(t):e(o)})})}return this._fullfilledPromise.then(t,e)},re.prototype.catch=function(t){return this.then(void 0,t)},re.prototype.use=function(t){return t(this),this},re.prototype.ok=function(t){if("function"!=typeof t)throw new Error("Callback required");return this._okCallback=t,this},re.prototype._isResponseOK=function(t){return!!t&&(this._okCallback?this._okCallback(t):t.status>=200&&t.status<300)},re.prototype.get=function(t){return this._header[t.toLowerCase()]},re.prototype.getHeader=re.prototype.get,re.prototype.set=function(t,e){if(te(t)){for(const e in t)ee(t,e)&&this.set(e,t[e]);return this}return this._header[t.toLowerCase()]=e,this.header[t]=e,this},re.prototype.unset=function(t){return delete this._header[t.toLowerCase()],delete this.header[t],this},re.prototype.field=function(t,e,r){if(null==t)throw new Error(".field(name, val) name can not be empty");if(this._data)throw new Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(te(t)){for(const e in t)ee(t,e)&&this.field(e,t[e]);return this}if(Array.isArray(e)){for(const r in e)ee(e,r)&&this.field(t,e[r]);return this}if(null==e)throw new Error(".field(name, val) val can not be empty");return"boolean"==typeof e&&(e=String(e)),r?this._getFormData().append(t,e,r):this._getFormData().append(t,e),this},re.prototype.abort=function(){return this._aborted||(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req&&this.req.abort(),this.clearTimeout(),this.emit("abort")),this},re.prototype._auth=function(t,e,r,o){switch(r.type){case"basic":this.set("Authorization","Basic "+o(`${t}:${e}`));break;case"auto":this.username=t,this.password=e;break;case"bearer":this.set("Authorization","Bearer "+t)}return this},re.prototype.withCredentials=function(t){return void 0===t&&(t=!0),this._withCredentials=t,this},re.prototype.redirects=function(t){return this._maxRedirects=t,this},re.prototype.maxResponseSize=function(t){if("number"!=typeof t)throw new TypeError("Invalid argument");return this._maxResponseSize=t,this},re.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},re.prototype.send=function(t){const e=te(t);let r=this._header["content-type"];if(this._formData)throw new Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(e&&!this._data)Array.isArray(t)?this._data=[]:this._isHost(t)||(this._data={});else if(t&&this._data&&this._isHost(this._data))throw new Error("Can't merge these send calls");if(e&&te(this._data))for(const o in t){if("bigint"==typeof t[o]&&!t[o].toJSON)throw new Error("Cannot serialize BigInt value to json");ee(t,o)&&(this._data[o]=t[o])}else{if("bigint"==typeof t)throw new Error("Cannot send value of type BigInt");"string"==typeof t?(r||this.type("form"),(r=this._header["content-type"])&&(r=r.toLowerCase().trim()),this._data="application/x-www-form-urlencoded"===r?this._data?`${this._data}&${t}`:t:(this._data||"")+t):this._data=t}return!e||this._isHost(t)||r||this.type("json"),this},re.prototype.sortQuery=function(t){return this._sort=void 0===t||t,this},re.prototype._finalizeQueryString=function(){const t=this._query.join("&");if(t&&(this.url+=(this.url.includes("?")?"&":"?")+t),this._query.length=0,this._sort){const t=this.url.indexOf("?");if(t>=0){const e=this.url.slice(t+1).split("&");"function"==typeof this._sort?e.sort(this._sort):e.sort(),this.url=this.url.slice(0,t)+"?"+e.join("&")}}},re.prototype._appendQueryString=()=>{console.warn("Unsupported")},re.prototype._timeoutError=function(t,e,r){if(this._aborted)return;const o=new Error(t+e+"ms exceeded");o.timeout=e,o.code="ECONNABORTED",o.errno=r,this.timedout=!0,this.timedoutError=o,this.abort(),this.callback(o)},re.prototype._setTimeouts=function(){const t=this;this._timeout&&!this._timer&&(this._timer=setTimeout(()=>{t._timeoutError("Timeout of ",t._timeout,"ETIME")},this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout(()=>{t._timeoutError("Response timeout of ",t._responseTimeout,"ETIMEDOUT")},this._responseTimeout))};var ie;function ae(){}ie=ae,ae.prototype.get=function(t){return this.header[t.toLowerCase()]},ae.prototype._setHeaderProperties=function(t){const e=t["content-type"]||"";this.type=Yt.type(e);const r=Yt.params(e);for(const n in r)Object.prototype.hasOwnProperty.call(r,n)&&(this[n]=r[n]);this.links={};try{t.link&&(this.links=Yt.parseLinks(t.link))}catch(o){}},ae.prototype._setStatusProperties=function(t){const e=Math.trunc(t/100);this.statusCode=t,this.status=this.statusCode,this.statusType=e,this.info=1===e,this.ok=2===e,this.redirect=3===e,this.clientError=4===e,this.serverError=5===e,this.error=(4===e||5===e)&&this.toError(),this.created=201===t,this.accepted=202===t,this.noContent=204===t,this.badRequest=400===t,this.unauthorized=401===t,this.notAcceptable=406===t,this.forbidden=403===t,this.notFound=404===t,this.unprocessableEntity=422===t};const se=["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert","disableTLSCerts"];class pe{constructor(){this._defaults=[]}_setDefaults(t){for(const e of this._defaults)t[e.fn](...e.args)}}for(const je of se)pe.prototype[je]=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return this._defaults.push({fn:je,args:e}),this};var ue=pe,le={};let ce;"undefined"!=typeof window?ce=window:"undefined"==typeof self?(console.warn("Using browser-only version of superagent in non-browser environment"),ce=this):ce=self;const{isObject:fe,mixin:ye,hasOwn:he}=Yt;function de(){}const me=le=le=function(t,e){return"function"==typeof e?new le.Request("GET",t).end(e):1===arguments.length?new le.Request("GET",t):new le.Request(t,e)};le.Request=Ae,me.getXHR=()=>{if(ce.XMLHttpRequest)return new ce.XMLHttpRequest;throw new Error("Browser-only version of superagent could not find XHR")};const ge="".trim?t=>t.trim():t=>t.replace(/(^\s*|\s*$)/g,"");function be(t){if(!fe(t))return t;const e=[];for(const r in t)he(t,r)&&ve(e,r,t[r]);return e.join("&")}function ve(t,e,r){if(void 0!==r)if(null!==r)if(Array.isArray(r))for(const o of r)ve(t,e,o);else if(fe(r))for(const o in r)he(r,o)&&ve(t,`${e}[${o}]`,r[o]);else t.push(encodeURI(e)+"="+encodeURIComponent(r));else t.push(encodeURI(e))}function we(t){const e={},r=t.split("&");let o,n;for(let i=0,a=r.length;i<a;++i)-1===(n=(o=r[i]).indexOf("="))?e[decodeURIComponent(o)]="":e[decodeURIComponent(o.slice(0,n))]=decodeURIComponent(o.slice(n+1));return e}function _e(t){return/[/+]json($|[^-\w])/i.test(t)}function Se(t){this.req=t,this.xhr=this.req.xhr,this.text="HEAD"!==this.req.method&&(""===this.xhr.responseType||"text"===this.xhr.responseType)||void 0===this.xhr.responseType?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText;let{status:e}=this.xhr;1223===e&&(e=204),this._setStatusProperties(e),this.headers=function(t){const e=t.split(/\r?\n/),r={};let o,n,i,a;for(let s=0,p=e.length;s<p;++s)-1!==(o=(n=e[s]).indexOf(":"))&&(i=n.slice(0,o).toLowerCase(),a=ge(n.slice(o+1)),r[i]=a);return r}(this.xhr.getAllResponseHeaders()),this.header=this.headers,this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this._setHeaderProperties(this.header),null===this.text&&t._responseType?this.body=this.xhr.response:this.body="HEAD"===this.req.method?null:this._parseBody(this.text?this.text:this.xhr.response)}function Ae(t,e){const r=this;this._query=this._query||[],this.method=t,this.url=e,this.header={},this._header={},this.on("end",()=>{let t,e=null,o=null;try{o=new Se(r)}catch(n){return(e=new Error("Parser is unable to parse the response")).parse=!0,e.original=n,r.xhr?(e.rawResponse=void 0===r.xhr.responseType?r.xhr.responseText:r.xhr.response,e.status=r.xhr.status?r.xhr.status:null,e.statusCode=e.status):(e.rawResponse=null,e.status=null),r.callback(e)}r.emit("response",o);try{r._isResponseOK(o)||(t=new Error(o.statusText||o.text||"Unsuccessful HTTP response"))}catch(n){t=n}t?(t.original=e,t.response=o,t.status=t.status||o.status,r.callback(t,o)):r.callback(null,o)})}me.serializeObject=be,me.parseString=we,me.types={html:"text/html",json:"application/json",xml:"text/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},me.serialize={"application/x-www-form-urlencoded":t=>Xt(t,{indices:!1,strictNullHandling:!0}),"application/json":r},me.parse={"application/x-www-form-urlencoded":we,"application/json":JSON.parse},ye(Se.prototype,ie.prototype),Se.prototype._parseBody=function(t){let e=me.parse[this.type];return this.req._parser?this.req._parser(this,t):(!e&&_e(this.type)&&(e=me.parse["application/json"]),e&&t&&(t.length>0||t instanceof Object)?e(t):null)},Se.prototype.toError=function(){const{req:t}=this,{method:e}=t,{url:r}=t,o=`cannot ${e} ${r} (${this.status})`,n=new Error(o);return n.status=this.status,n.method=e,n.url=r,n},me.Response=Se,t(Ae.prototype),ye(Ae.prototype,Zt.prototype),Ae.prototype.type=function(t){return this.set("Content-Type",me.types[t]||t),this},Ae.prototype.accept=function(t){return this.set("Accept",me.types[t]||t),this},Ae.prototype.auth=function(t,e,r){1===arguments.length&&(e=""),"object"==typeof e&&null!==e&&(r=e,e=""),r||(r={type:"function"==typeof btoa?"basic":"auto"});const o=r.encoder?r.encoder:t=>{if("function"==typeof btoa)return btoa(t);throw new Error("Cannot use basic auth, btoa is not a function")};return this._auth(t,e,r,o)},Ae.prototype.query=function(t){return"string"!=typeof t&&(t=be(t)),t&&this._query.push(t),this},Ae.prototype.attach=function(t,e,r){if(e){if(this._data)throw new Error("superagent can't mix .send() and .attach()");this._getFormData().append(t,e,r||e.name)}return this},Ae.prototype._getFormData=function(){return this._formData||(this._formData=new ce.FormData),this._formData},Ae.prototype.callback=function(t,e){if(this._shouldRetry(t,e))return this._retry();const r=this._callback;this.clearTimeout(),t&&(this._maxRetries&&(t.retries=this._retries-1),this.emit("error",t)),r(t,e)},Ae.prototype.crossDomainError=function(){const t=new Error("Request has been terminated\nPossible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.");t.crossDomain=!0,t.status=this.status,t.method=this.method,t.url=this.url,this.callback(t)},Ae.prototype.agent=function(){return console.warn("This is not supported in browser version of superagent"),this},Ae.prototype.ca=Ae.prototype.agent,Ae.prototype.buffer=Ae.prototype.ca,Ae.prototype.write=()=>{throw new Error("Streaming is not supported in browser version of superagent")},Ae.prototype.pipe=Ae.prototype.write,Ae.prototype._isHost=function(t){return t&&"object"==typeof t&&!Array.isArray(t)&&"[object Object]"!==Object.prototype.toString.call(t)},Ae.prototype.end=function(t){this._endCalled&&console.warn("Warning: .end() was called twice. This is not supported in superagent"),this._endCalled=!0,this._callback=t||de,this._finalizeQueryString(),this._end()},Ae.prototype._setUploadTimeout=function(){const t=this;this._uploadTimeout&&!this._uploadTimeoutTimer&&(this._uploadTimeoutTimer=setTimeout(()=>{t._timeoutError("Upload timeout of ",t._uploadTimeout,"ETIMEDOUT")},this._uploadTimeout))},Ae.prototype._end=function(){if(this._aborted)return this.callback(new Error("The request has been aborted even before .end() was called"));const t=this;this.xhr=me.getXHR();const{xhr:e}=this;let r=this._formData||this._data;this._setTimeouts(),e.addEventListener("readystatechange",()=>{const{readyState:r}=e;if(r>=2&&t._responseTimeoutTimer&&clearTimeout(t._responseTimeoutTimer),4!==r)return;let o;try{o=e.status}catch(n){o=0}if(!o){if(t.timedout||t._aborted)return;return t.crossDomainError()}t.emit("end")});const o=(e,r)=>{r.total>0&&(r.percent=r.loaded/r.total*100,100===r.percent&&clearTimeout(t._uploadTimeoutTimer)),r.direction=e,t.emit("progress",r)};if(this.hasListeners("progress"))try{e.addEventListener("progress",o.bind(null,"download")),e.upload&&e.upload.addEventListener("progress",o.bind(null,"upload"))}catch(n){}e.upload&&this._setUploadTimeout();try{this.username&&this.password?e.open(this.method,this.url,!0,this.username,this.password):e.open(this.method,this.url,!0)}catch(n){return this.callback(n)}if(this._withCredentials&&(e.withCredentials=!0),!this._formData&&"GET"!==this.method&&"HEAD"!==this.method&&"string"!=typeof r&&!this._isHost(r)){const t=this._header["content-type"];let e=this._serializer||me.serialize[t?t.split(";")[0]:""];!e&&_e(t)&&(e=me.serialize["application/json"]),e&&(r=e(r))}for(const i in this.header)null!==this.header[i]&&he(this.header,i)&&e.setRequestHeader(i,this.header[i]);this._responseType&&(e.responseType=this._responseType),this.emit("request",this),e.send(void 0===r?null:r)},me.agent=()=>new ue;for(const je of["GET","POST","OPTIONS","PATCH","PUT","DELETE"])ue.prototype[je.toLowerCase()]=function(t,e){const r=new me.Request(je,t);return this._setDefaults(r),e&&r.end(e),r};function Ee(t,e,r){const o=me("DELETE",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o}return ue.prototype.del=ue.prototype.delete,me.get=(t,e,r)=>{const o=me("GET",t);return"function"==typeof e&&(r=e,e=null),e&&o.query(e),r&&o.end(r),o},me.head=(t,e,r)=>{const o=me("HEAD",t);return"function"==typeof e&&(r=e,e=null),e&&o.query(e),r&&o.end(r),o},me.options=(t,e,r)=>{const o=me("OPTIONS",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},me.del=Ee,me.delete=Ee,me.patch=(t,e,r)=>{const o=me("PATCH",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},me.post=(t,e,r)=>{const o=me("POST",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},me.put=(t,e,r)=>{const o=me("PUT",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},le}));