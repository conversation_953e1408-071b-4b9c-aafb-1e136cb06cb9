name: CI
on:
  push:
    paths-ignore:
        - 'docs/**'
        - '*.md'
  pull_request:
    paths-ignore:
        - 'docs/**'
        - '*.md'
jobs:
  test:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        node-version: [14, 16, 18, 20]
        os: [macos-latest, ubuntu-latest, windows-latest]
        exclude:
          - node-version: 14
            os: windows-latest

    steps:

      - uses: actions/checkout@v4

      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install
        run: |
          npm install --ignore-scripts

      - name: Run tests
        run: |
          npm run test

  automerge:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: fastify/github-action-merge-dependabot@v3.9
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
