{"version": 3, "names": ["_assert", "require", "_t", "callExpression", "cloneNode", "expressionStatement", "identifier", "importDeclaration", "importDefaultSpecifier", "importNamespaceSpecifier", "importSpecifier", "memberExpression", "stringLiteral", "variableDeclaration", "variableDeclarator", "ImportBuilder", "constructor", "importedSource", "scope", "hub", "_statements", "_resultName", "_importedSource", "_scope", "_hub", "done", "statements", "resultName", "import", "push", "namespace", "name", "local", "generateUidIdentifier", "statement", "length", "assert", "type", "specifiers", "default", "id", "named", "importName", "var", "expression", "defaultInterop", "_interop", "addHelper", "wildcardInterop", "callee", "declarations", "init", "fail", "prop", "read", "exports"], "sources": ["../src/import-builder.ts"], "sourcesContent": ["import assert from \"node:assert\";\nimport {\n  callExpression,\n  cloneNode,\n  expressionStatement,\n  identifier,\n  importDeclaration,\n  importDefaultSpecifier,\n  importNamespaceSpecifier,\n  importSpecifier,\n  memberExpression,\n  stringLiteral,\n  variableDeclaration,\n  variableDeclarator,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { Scope, HubInterface } from \"@babel/traverse\";\n\n/**\n * A class to track and accumulate mutations to the AST that will eventually\n * output a new require/import statement list.\n */\nexport default class ImportBuilder {\n  private _statements: t.Statement[] = [];\n  private _resultName: t.Identifier | t.MemberExpression = null;\n\n  declare _scope: Scope;\n  declare _hub: HubInterface;\n  private _importedSource: string;\n\n  constructor(importedSource: string, scope: Scope, hub: HubInterface) {\n    this._scope = scope;\n    this._hub = hub;\n    this._importedSource = importedSource;\n  }\n\n  done() {\n    return {\n      statements: this._statements,\n      resultName: this._resultName,\n    };\n  }\n\n  import() {\n    this._statements.push(\n      importDeclaration([], stringLiteral(this._importedSource)),\n    );\n    return this;\n  }\n\n  require() {\n    this._statements.push(\n      expressionStatement(\n        callExpression(identifier(\"require\"), [\n          stringLiteral(this._importedSource),\n        ]),\n      ),\n    );\n    return this;\n  }\n\n  namespace(name = \"namespace\") {\n    const local = this._scope.generateUidIdentifier(name);\n\n    const statement = this._statements[this._statements.length - 1];\n    assert(statement.type === \"ImportDeclaration\");\n    assert(statement.specifiers.length === 0);\n    statement.specifiers = [importNamespaceSpecifier(local)];\n    this._resultName = cloneNode(local);\n    return this;\n  }\n  default(name: string) {\n    const id = this._scope.generateUidIdentifier(name);\n    const statement = this._statements[this._statements.length - 1];\n    assert(statement.type === \"ImportDeclaration\");\n    assert(statement.specifiers.length === 0);\n    statement.specifiers = [importDefaultSpecifier(id)];\n    this._resultName = cloneNode(id);\n    return this;\n  }\n  named(name: string, importName: string) {\n    if (importName === \"default\") return this.default(name);\n\n    const id = this._scope.generateUidIdentifier(name);\n    const statement = this._statements[this._statements.length - 1];\n    assert(statement.type === \"ImportDeclaration\");\n    assert(statement.specifiers.length === 0);\n    statement.specifiers = [importSpecifier(id, identifier(importName))];\n    this._resultName = cloneNode(id);\n    return this;\n  }\n\n  var(name: string) {\n    const id = this._scope.generateUidIdentifier(name);\n    let statement = this._statements[this._statements.length - 1];\n    if (statement.type !== \"ExpressionStatement\") {\n      assert(this._resultName);\n      statement = expressionStatement(this._resultName);\n      this._statements.push(statement);\n    }\n    this._statements[this._statements.length - 1] = variableDeclaration(\"var\", [\n      variableDeclarator(id, statement.expression),\n    ]);\n    this._resultName = cloneNode(id);\n    return this;\n  }\n\n  defaultInterop() {\n    return this._interop(this._hub.addHelper(\"interopRequireDefault\"));\n  }\n  wildcardInterop() {\n    return this._interop(this._hub.addHelper(\"interopRequireWildcard\"));\n  }\n\n  _interop(callee: t.Expression) {\n    const statement = this._statements[this._statements.length - 1];\n    if (statement.type === \"ExpressionStatement\") {\n      statement.expression = callExpression(callee, [statement.expression]);\n    } else if (statement.type === \"VariableDeclaration\") {\n      assert(statement.declarations.length === 1);\n      statement.declarations[0].init = callExpression(callee, [\n        statement.declarations[0].init,\n      ]);\n    } else {\n      assert.fail(\"Unexpected type.\");\n    }\n    return this;\n  }\n\n  prop(name: string) {\n    const statement = this._statements[this._statements.length - 1];\n    if (statement.type === \"ExpressionStatement\") {\n      statement.expression = memberExpression(\n        statement.expression,\n        identifier(name),\n      );\n    } else if (statement.type === \"VariableDeclaration\") {\n      assert(statement.declarations.length === 1);\n      statement.declarations[0].init = memberExpression(\n        statement.declarations[0].init,\n        identifier(name),\n      );\n    } else {\n      assert.fail(\"Unexpected type:\" + statement.type);\n    }\n    return this;\n  }\n\n  read(name: string) {\n    this._resultName = memberExpression(this._resultName, identifier(name));\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,EAAA,GAAAD,OAAA;AAasB;EAZpBE,cAAc;EACdC,SAAS;EACTC,mBAAmB;EACnBC,UAAU;EACVC,iBAAiB;EACjBC,sBAAsB;EACtBC,wBAAwB;EACxBC,eAAe;EACfC,gBAAgB;EAChBC,aAAa;EACbC,mBAAmB;EACnBC;AAAkB,IAAAZ,EAAA;AASL,MAAMa,aAAa,CAAC;EAQjCC,WAAWA,CAACC,cAAsB,EAAEC,KAAY,EAAEC,GAAiB,EAAE;IAAA,KAP7DC,WAAW,GAAkB,EAAE;IAAA,KAC/BC,WAAW,GAAsC,IAAI;IAAA,KAIrDC,eAAe;IAGrB,IAAI,CAACC,MAAM,GAAGL,KAAK;IACnB,IAAI,CAACM,IAAI,GAAGL,GAAG;IACf,IAAI,CAACG,eAAe,GAAGL,cAAc;EACvC;EAEAQ,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,UAAU,EAAE,IAAI,CAACN,WAAW;MAC5BO,UAAU,EAAE,IAAI,CAACN;IACnB,CAAC;EACH;EAEAO,MAAMA,CAAA,EAAG;IACP,IAAI,CAACR,WAAW,CAACS,IAAI,CACnBtB,iBAAiB,CAAC,EAAE,EAAEK,aAAa,CAAC,IAAI,CAACU,eAAe,CAAC,CAC3D,CAAC;IACD,OAAO,IAAI;EACb;EAEArB,OAAOA,CAAA,EAAG;IACR,IAAI,CAACmB,WAAW,CAACS,IAAI,CACnBxB,mBAAmB,CACjBF,cAAc,CAACG,UAAU,CAAC,SAAS,CAAC,EAAE,CACpCM,aAAa,CAAC,IAAI,CAACU,eAAe,CAAC,CACpC,CACH,CACF,CAAC;IACD,OAAO,IAAI;EACb;EAEAQ,SAASA,CAACC,IAAI,GAAG,WAAW,EAAE;IAC5B,MAAMC,KAAK,GAAG,IAAI,CAACT,MAAM,CAACU,qBAAqB,CAACF,IAAI,CAAC;IAErD,MAAMG,SAAS,GAAG,IAAI,CAACd,WAAW,CAAC,IAAI,CAACA,WAAW,CAACe,MAAM,GAAG,CAAC,CAAC;IAC/DC,OAAM,CAACF,SAAS,CAACG,IAAI,KAAK,mBAAmB,CAAC;IAC9CD,OAAM,CAACF,SAAS,CAACI,UAAU,CAACH,MAAM,KAAK,CAAC,CAAC;IACzCD,SAAS,CAACI,UAAU,GAAG,CAAC7B,wBAAwB,CAACuB,KAAK,CAAC,CAAC;IACxD,IAAI,CAACX,WAAW,GAAGjB,SAAS,CAAC4B,KAAK,CAAC;IACnC,OAAO,IAAI;EACb;EACAO,OAAOA,CAACR,IAAY,EAAE;IACpB,MAAMS,EAAE,GAAG,IAAI,CAACjB,MAAM,CAACU,qBAAqB,CAACF,IAAI,CAAC;IAClD,MAAMG,SAAS,GAAG,IAAI,CAACd,WAAW,CAAC,IAAI,CAACA,WAAW,CAACe,MAAM,GAAG,CAAC,CAAC;IAC/DC,OAAM,CAACF,SAAS,CAACG,IAAI,KAAK,mBAAmB,CAAC;IAC9CD,OAAM,CAACF,SAAS,CAACI,UAAU,CAACH,MAAM,KAAK,CAAC,CAAC;IACzCD,SAAS,CAACI,UAAU,GAAG,CAAC9B,sBAAsB,CAACgC,EAAE,CAAC,CAAC;IACnD,IAAI,CAACnB,WAAW,GAAGjB,SAAS,CAACoC,EAAE,CAAC;IAChC,OAAO,IAAI;EACb;EACAC,KAAKA,CAACV,IAAY,EAAEW,UAAkB,EAAE;IACtC,IAAIA,UAAU,KAAK,SAAS,EAAE,OAAO,IAAI,CAACH,OAAO,CAACR,IAAI,CAAC;IAEvD,MAAMS,EAAE,GAAG,IAAI,CAACjB,MAAM,CAACU,qBAAqB,CAACF,IAAI,CAAC;IAClD,MAAMG,SAAS,GAAG,IAAI,CAACd,WAAW,CAAC,IAAI,CAACA,WAAW,CAACe,MAAM,GAAG,CAAC,CAAC;IAC/DC,OAAM,CAACF,SAAS,CAACG,IAAI,KAAK,mBAAmB,CAAC;IAC9CD,OAAM,CAACF,SAAS,CAACI,UAAU,CAACH,MAAM,KAAK,CAAC,CAAC;IACzCD,SAAS,CAACI,UAAU,GAAG,CAAC5B,eAAe,CAAC8B,EAAE,EAAElC,UAAU,CAACoC,UAAU,CAAC,CAAC,CAAC;IACpE,IAAI,CAACrB,WAAW,GAAGjB,SAAS,CAACoC,EAAE,CAAC;IAChC,OAAO,IAAI;EACb;EAEAG,GAAGA,CAACZ,IAAY,EAAE;IAChB,MAAMS,EAAE,GAAG,IAAI,CAACjB,MAAM,CAACU,qBAAqB,CAACF,IAAI,CAAC;IAClD,IAAIG,SAAS,GAAG,IAAI,CAACd,WAAW,CAAC,IAAI,CAACA,WAAW,CAACe,MAAM,GAAG,CAAC,CAAC;IAC7D,IAAID,SAAS,CAACG,IAAI,KAAK,qBAAqB,EAAE;MAC5CD,OAAM,CAAC,IAAI,CAACf,WAAW,CAAC;MACxBa,SAAS,GAAG7B,mBAAmB,CAAC,IAAI,CAACgB,WAAW,CAAC;MACjD,IAAI,CAACD,WAAW,CAACS,IAAI,CAACK,SAAS,CAAC;IAClC;IACA,IAAI,CAACd,WAAW,CAAC,IAAI,CAACA,WAAW,CAACe,MAAM,GAAG,CAAC,CAAC,GAAGtB,mBAAmB,CAAC,KAAK,EAAE,CACzEC,kBAAkB,CAAC0B,EAAE,EAAEN,SAAS,CAACU,UAAU,CAAC,CAC7C,CAAC;IACF,IAAI,CAACvB,WAAW,GAAGjB,SAAS,CAACoC,EAAE,CAAC;IAChC,OAAO,IAAI;EACb;EAEAK,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACtB,IAAI,CAACuB,SAAS,CAAC,uBAAuB,CAAC,CAAC;EACpE;EACAC,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACF,QAAQ,CAAC,IAAI,CAACtB,IAAI,CAACuB,SAAS,CAAC,wBAAwB,CAAC,CAAC;EACrE;EAEAD,QAAQA,CAACG,MAAoB,EAAE;IAC7B,MAAMf,SAAS,GAAG,IAAI,CAACd,WAAW,CAAC,IAAI,CAACA,WAAW,CAACe,MAAM,GAAG,CAAC,CAAC;IAC/D,IAAID,SAAS,CAACG,IAAI,KAAK,qBAAqB,EAAE;MAC5CH,SAAS,CAACU,UAAU,GAAGzC,cAAc,CAAC8C,MAAM,EAAE,CAACf,SAAS,CAACU,UAAU,CAAC,CAAC;IACvE,CAAC,MAAM,IAAIV,SAAS,CAACG,IAAI,KAAK,qBAAqB,EAAE;MACnDD,OAAM,CAACF,SAAS,CAACgB,YAAY,CAACf,MAAM,KAAK,CAAC,CAAC;MAC3CD,SAAS,CAACgB,YAAY,CAAC,CAAC,CAAC,CAACC,IAAI,GAAGhD,cAAc,CAAC8C,MAAM,EAAE,CACtDf,SAAS,CAACgB,YAAY,CAAC,CAAC,CAAC,CAACC,IAAI,CAC/B,CAAC;IACJ,CAAC,MAAM;MACLf,OAAM,CAACgB,IAAI,CAAC,kBAAkB,CAAC;IACjC;IACA,OAAO,IAAI;EACb;EAEAC,IAAIA,CAACtB,IAAY,EAAE;IACjB,MAAMG,SAAS,GAAG,IAAI,CAACd,WAAW,CAAC,IAAI,CAACA,WAAW,CAACe,MAAM,GAAG,CAAC,CAAC;IAC/D,IAAID,SAAS,CAACG,IAAI,KAAK,qBAAqB,EAAE;MAC5CH,SAAS,CAACU,UAAU,GAAGjC,gBAAgB,CACrCuB,SAAS,CAACU,UAAU,EACpBtC,UAAU,CAACyB,IAAI,CACjB,CAAC;IACH,CAAC,MAAM,IAAIG,SAAS,CAACG,IAAI,KAAK,qBAAqB,EAAE;MACnDD,OAAM,CAACF,SAAS,CAACgB,YAAY,CAACf,MAAM,KAAK,CAAC,CAAC;MAC3CD,SAAS,CAACgB,YAAY,CAAC,CAAC,CAAC,CAACC,IAAI,GAAGxC,gBAAgB,CAC/CuB,SAAS,CAACgB,YAAY,CAAC,CAAC,CAAC,CAACC,IAAI,EAC9B7C,UAAU,CAACyB,IAAI,CACjB,CAAC;IACH,CAAC,MAAM;MACLK,OAAM,CAACgB,IAAI,CAAC,kBAAkB,GAAGlB,SAAS,CAACG,IAAI,CAAC;IAClD;IACA,OAAO,IAAI;EACb;EAEAiB,IAAIA,CAACvB,IAAY,EAAE;IACjB,IAAI,CAACV,WAAW,GAAGV,gBAAgB,CAAC,IAAI,CAACU,WAAW,EAAEf,UAAU,CAACyB,IAAI,CAAC,CAAC;EACzE;AACF;AAACwB,OAAA,CAAAhB,OAAA,GAAAxB,aAAA", "ignoreList": []}