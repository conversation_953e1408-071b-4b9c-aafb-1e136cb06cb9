# Monitora Web App [![E2E Tests (production)](https://github.com/monitora-media/monitora-frontend/actions/workflows/e2e_tests_prod.yml/badge.svg)](https://github.com/monitora-media/monitora-frontend/actions/workflows/e2e_tests_prod.yml) [![E2E Tests](https://github.com/monitora-media/monitora-frontend/actions/workflows/integration_tests.yml/badge.svg)](https://github.com/monitora-media/monitora-frontend/actions/workflows/integration_tests.yml)

## Prerequisites

- Docker Desktop

  - **_capability: dev, build; unit, integration, e2e tests w/ visual comparisons_**
  - macOS & Homebrew: `brew install --cask docker`

> [!TIP]
> App development is fully supported via our [Dev Container](.devcontainer/devcontainer.json) (think of it as a tailored IDE). Keep in mind that performance is still better when running locally. While our Docker setup does have all capabilities, an optimal workflow on a permanent host machine would be to combine Docker and local methods. For sporadic testing (e.g. need to debug something on Windows) or when on throwaway hosts (e.g. you're actually a spy, but need to fix something quick on MB to maintain your cover), Dev Container and your editor are your friends.

- Tailscale

  - If plan on running E2E tests, install [Tailscale](https://tailscale.com/).
    - macOS & Homebrew: `brew install --cask tailscale` or from [App Store](https://apps.apple.com/cz/app/tailscale/id1475387142?mt=12)

All following prerequisites are included in our Docker images, so if Docker-only is your choice, you can [skip ahead to usage via Docker](#usage-via-docker).

---

If you prefer to work locally, you will need:

- Node.js

  - _capability: dev, build; unit, integration, e2e tests w/o visual comparisons_
  - We enforce a minimum node version, usually in the active LTS range. It is specified in the [package.json](package.json) `engines.node` field.
  - A version manager (e.g. https://github.com/Schniz/fnm) is recommended for easier install & version switching.
  - After installing a new version, make sure to run `corepack enable`. This will give you access to the `pnpm` binary, without needing to install it globally.

- Git LFS

  - _capability: dev, build_
  - If you're going to push commits, install [Git LFS](https://git-lfs.github.com/).
    - ❗️ You only need to install the binaries, no other steps should be necessary.
    - macOS & Homebrew: `brew install git-lfs`

- ImageMagick

  - _capability: dev, build_
  - If you plan on editing [statics](#static-assets)
  - macOS & Homebrew: `brew install imagemagick`
  - (optional) configure `LC_CTYPE` or `LC_ALL` in your shell config, eg.:
    ```bash
    export LC_CTYPE="cs_CZ.UTF-8"
    ```

### Example setup (macOS, local)

```bash
$ brew install fnm
# 1. https://github.com/Schniz/fnm#shell-setup
# 2. restart your shell
$ cd monitora-frontend/
$ fnm install --corepack-enabled --resolve-engines
$ pnpm install
```

## Usage

### Clone or update

```bash
$ cd monitora-frontend/
$ git checkout dev
$ git pull

# Run against the production API:
$ grep API_URL .env.local.example >> .env.local
```

### Usage via Docker

#### Build & start

> [!IMPORTANT]
> Ensure your Django project includes [`'host.docker.internal'`](https://docs.docker.com/desktop/features/networking/#i-want-to-connect-from-a-container-to-a-service-on-the-host) in (local) settings `ALLOWED_HOSTS`.

```bash
# Run against Django dev server:
$ echo 'API_URL=http://host.docker.internal:8000/api/v1' >> .env.local

$ ./docker compose up app
# or
$ ./docker compose up app -d
# or
$ ./docker compose up app -d --build # rebuilds
```

#### [Convenience scripts](Makefile) (macOS, Linux)

Mostly shortcuts for clean up.

```bash
$ make stop
# or
$ make clean:repo
# etc.
```

#### `package.json` scripts

For this purpose we expose the `tools` service, which can run our one-off `package.json` scripts.

<!-- prettier-ignore-start -->
```bash
$ ./docker compose run tools pnpm fmt <file> ...
$ ./docker compose run tools pnpm lint
$ ./docker compose run tools pnpm lint:css
$ ./docker compose run tools pnpm test src/         # unit tests
$ ./docker compose run tools pnpm test integration/ # works after copying app from container, see example in `.github/workflows/pull_request.yml`
```
<!-- prettier-ignore-end -->

### Usage (local)

```bash
# Run against Django dev server:
$ echo 'API_URL=http://localhost:8000/api/v1' >> .env.local

$ pnpm install
$ pnpm build
$ pnpm start
```

## Run E2E tests

> [!IMPORTANT]
>
> - Add a GitHub token to your environment. The easiest way is to reuse the [GitHub CLI](https://cli.github.com/) token:
>   - `echo GITHUB_TOKEN=$(gh auth token) >> .env.test.local`
>     - might need to `gh auth login` and follow prompts first
> - Ask @infra to add your SSH key, which [you've added to your GitHub account](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/adding-a-new-ssh-key-to-your-github-account), to the `integration-tests` server ([example](https://github.com/stovmascript.keys)).
> - Make sure you're connected to the [VPN](https://imper.slack.com/archives/C058DL1CFKL/p1706608447088199).

```bash
# Install/update Playwright browsers to run E2E tests locally:
$ pnpm playwright install --with-deps

# Run all tests
$ pnpm test:e2e

# Only run test specs in a specific folder
$ pnpm test:e2e e2e/pages/user/

# Only run a single test spec
$ pnpm test:e2e e2e/pages/logout.spec.ts

# Only run tests with the "article" string either in its title or filename
$ pnpm test:e2e --grep article

# Only run tests "/staff/admin" tests as the staff user
$ pnpm test:e2e --grep '/staff/admin.*as staff'

# Only run tests tagged as smoke tests
$ pnpm test:e2e --grep @smoke

# Only run tests tagged as smoke tests on mobile
$ pnpm test:e2e --grep @smoke --project 'Mobile Safari'

# Run everything but smoke tests on desktop
$ pnpm test:e2e --grep-invert @smoke --project chromium

# Run in UI mode including `pnpm dev` for faster iteration
$ pnpm test:e2e:dev

# To run tests in Docker, replace `pnpm test:e2e` with:
$ ./docker compose run e2e
```

### Visual comparisons

This is a Docker-only/CI feature. Snapshots are ignored when running locally (`pnpm test:e2e` examples) because they will differ from platform to platform.

```bash
# Only run tests which produce snapshots
$ ./docker compose run e2e --grep @screenshot

# Run tests which produce snapshots and force customer screenshots
$ ./docker compose run -e E2E_FORCE_CUSTOMER_SCREENSHOTS=all e2e --grep @screenshot
$ ./docker compose run -e E2E_FORCE_CUSTOMER_SCREENSHOTS=customerHero e2e --grep @screenshot

# Only update snapshots
$ ./docker compose run e2e --grep @screenshot -u
```

All expected file changes are synced back to the host machine, so you can `pnpm playwright show-report` (if you've [installed locally](#usage-local)), diff & commit updated snapshots, etc.

### Coverage

> [!IMPORTANT]
> You will need to rebuild the app with coverage instrumentation enabled. Make sure to stop any FE server you might have running locally.

```bash
# Always run all tests for an accurate report
$ COVERAGE=1 pnpm test:e2e # automatically produces a coverage report

# Get a coverage report after running tests in Docker
$ ./docker compose run --build -e COVERAGE=1 e2e
$ ./docker compose run nyc report
```

A text summary should be printed to the console. You can also open `e2e/artifacts/coverage/index.html` in your browser.

## (Static) Assets

- `src/assets/`
  - Source assets, optimized, prettified, for direct usage (`import`) or other programmatic (script) usage.
  - Possibly forked for specific usage.
- `src/assets/*/reference/`
  - Reference assets, optimized, prettified, for (in)direct usage. These files are important for their "geometry" and variation, as their designer intended.
- `public/`
  - The [home](https://nextjs.org/docs/app/building-your-application/optimizing/static-assets) for all static assets served by Next.js.
- `public/static/`
  - Special subfolder intercepted by nginx in production. Leverage this folder for boosted serving of statics.
  - ❗️ BE config must also be updated in case of moving it.

### [Workflow tips](https://github.com/monitora-media/monitora-frontend/wiki#workflow-tips-for-assets)

### Generate statics from assets

```bash
$ pnpm assets
```

### Update self-hosted Material Symbols

```bash
$ pnpm update:icons
```

If there's a newer version of Material Symbols available, this script will produce a new font in `public/static/` and update `src/styles/material-symbols.css` accordingly. Otherwise, no changes are made. Old font files can be deleted.

## Storybook

Update `.env.local` with your app login credentials before building.

```
STORYBOOK_USERNAME=...
STORYBOOK_PASSWORD=...
```

## Extract 500 error page for further deploy elsewhere

Happens automatically during a production build.

```bash
$ pnpm extract:500
```

This will output a `standalone/500` directory, with index.html and all static files needed to serve the page.

## Deployment

> [!CAUTION]
> Happens automatically on push to master via [GitHub Actions](https://github.com/monitora-media/monitora-frontend/actions/workflows/deploy.yml).

### Manual deployment via GitHub Actions

![Workflow dispatch](docs/deploy.png)

1. Go to [Deploy Dockerized Frontend to Production](https://github.com/monitora-media/monitora-frontend/actions/workflows/deploy.yml) GHA workflow.
2. Click on the "Run workflow" button.
3. Select correct options:

   - **Use workflow from**: ❗️ Select `master` branch (this is important for building the image - **all code has to be in master**, otherwise the [build will fail](https://github.com/monitora-media/monitora-frontend/blob/7ae515922d315d4826cdf802b2026edc5e3fade3/.github/actions/setup/docker/action.yml#L31)).
   - **image_sha**: If you would like to skip the build job and directly deploy a previously built image, enter the long commit hash of the image. The image has to be available in [GitHub Packages](https://github.com/monitora-media/monitora-frontend/pkgs/container/monitora-frontend%2Fapp/versions). You should be able to find a `git-<long commit hash>` tag there.

4. Click on `Run workflow`.
5. Make sure the deployment was completed successfully (see [#mediaboard-deployments](https://app.slack.com/client/T02SYD6BB/C072X1NRW07)).

#### Using GH CLI

Install and configure [GH CLI](https://cli.github.com/)

```
# pwd = monitora-frontend repo
gh workflow run deploy.yml [--raw-field image-sha=41517a3c32466cfad4efd3d37934bab114716b4aeba7a6ec39a9bc29e48f2147]
```

Optional arguments are explained in the section above.

### Rollback

Rollback uses the same [manual](#manual-deployment-via-github-actions) workflow dispatch mechanism as regular deployment:

![Rollback](docs/rollback.png)

#### Step 1: Figure out which image to deploy

The production Docker image for frontend is build on every merge to master.

1. Find the long commit hash of the _good_ merge commit on master you want to roll back to.
2. Verify that the image is in [GitHub Packages](https://github.com/monitora-media/monitora-frontend/pkgs/container/monitora-frontend%2Fapp/versions). You should be able to find a `git-<long commit hash>` tag there.

#### Step 2: Deploy rollback image

1. Go to [Deploy Dockerized Frontend to Production](https://github.com/monitora-media/monitora-frontend/actions/workflows/deploy.yml) GHA workflow.
2. Click on the `Run workflow` button.
3. Select correct options:

   - **Use workflow from**: Irrelevant.
   - **image_sha**: Enter the long commit hash of the image.

4. Click on `Run workflow`.
5. Make sure the deployment was completed successfully (see [#mediaboard-deployments](https://app.slack.com/client/T02SYD6BB/C072X1NRW07)).

### Container Logs

We currently use the [journald logging driver for Docker](https://docs.docker.com/engine/logging/drivers/journald/).

You can see logs from Docker containers:

```bash
$ ssh frontend
$ sudo journalctl -b IMAGE_NAME=mediaboard.com/frontend/app:production
```

TODO: If we start using centralized logging, syslog driver may be better.

### How to deploy in case of GitHub Actions outage

The [deployment script](https://github.com/monitora-media/monitora-infra/blob/master/ansible/roles/frontend-docker-deploy/templates/scripts/deploy-frontend.sh) can be triggered manually on the `frontend` VM. You will need to create a GitHub token and log into the Docker registry manually.

#### Step 1: Create a Docker registry token

1. [Generate a new personal access token (classic)](https://github.com/settings/tokens/new?description=Mediaboard%20Frontend%20Packages&scopes=read:packages) in GitHub settings.
2. Name the token.
3. Select the `read:packages` token scope.
4. Click `Generate token`.
5. Securely store the value of the token.

#### Step 2: Log in to GitHub Packages Docker registry and trigger deployment

<!-- prettier-ignore-start -->
```bash
$ ssh frontend
$ read -rs GH_TOKEN
# Input/paste the value of the token from step 1, press enter
$ echo $GH_TOKEN | sudo docker login ghcr.io -u <your github username> --password-stdin
# To deploy the latest Docker image tagged `production`
$ sudo deploy-frontend.sh
# To deploy a specific image
$ sudo APP_TAG=git-<long commit hash> deploy-frontend.sh
```
<!-- prettier-ignore-end -->

#### Step 3: Verify

1. Read the logs from the deployment script, confirm that the new container is up and the correct image was used.
2. Verify that https://app.mediaboard.com is up.
3. [Check container logs if needed](#container-logs).

#### Step 4: Log out of Docker registry

To remove your token which is stored in plaintext:

```bash
$ sudo docker logout
```

### Deployment resources

- [Frontend deployment method description](https://docs.google.com/document/d/1nwb-ve2mHkhrcm5BAaUPJltXuTemPhC3i4U0r4c0CKk/edit?usp=sharing)
- [Frontend Docker deploy Ansible role](https://github.com/monitora-media/monitora-infra/tree/master/ansible/roles/frontend-docker-deploy)
- [Deployment shitbook](https://github.com/monitora-media/monitora/wiki/Shitbook#deployment)

If you run into any issues, contact @stovmascript or @infra.
