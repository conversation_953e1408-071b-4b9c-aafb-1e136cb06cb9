# MediaBoard WebSocket Server

A simple real-time notification server for the Monitora application, built with Node.js, TypeScript, and WebSockets.

## Features

- **Real-time WebSocket connections** with automatic reconnection support
- **Simple message broadcasting** to all connected clients
- **HTTP API endpoint** for sending notifications
- **Health check endpoint** for monitoring
- **Connection management** with ping/pong keep-alive
- **Structured logging** with Pino
- **TypeScript support** with full type safety

## Quick Start

### 1. Installation

```bash
# Install dependencies
pnpm install

# Copy environment configuration
cp .env.example .env

# Edit .env with your configuration
nano .env
```

### 2. Development

```bash
# Start development server with hot reload
pnpm dev
```

### 3. Production

```bash
# Build the project
pnpm build

# Start production server
pnpm start
```

## Configuration

Configure the server using environment variables:

```env
# Server Configuration
PORT=8080
HOST=0.0.0.0
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=info
```

## API Endpoints

### Health Check
```
GET /health
```
Returns server health status and connection statistics.

### Send Notification
```
POST /notify
Content-Type: application/json

{
  "model": "AppNotificationsLog",
  "method": "CREATE",
  "data": [
    {
      "id": 1,
      "app_notification_type": 1,
      "title": "Test Notification",
      "icon": "info",
      "url": null,
      "created": "2024-01-01T00:00:00.000Z",
      "is_read": false
    }
  ]
}
```

## WebSocket Protocol

### Connection
Connect to `ws://localhost:8080/ws`

### Message Format
All messages sent to connected clients follow this format:
```json
{
  "model": "AppNotificationsLog",
  "method": "CREATE",
  "data": [
    {
      "id": 1,
      "app_notification_type": 1,
      "title": "Notification Title",
      "icon": "info",
      "url": null,
      "created": "2024-01-01T00:00:00.000Z",
      "is_read": false
    }
  ]
}
```
## Testing

Test the WebSocket server with the provided scripts:

```bash
# Test basic connection
pnpm test:connection

# Test notification sending
pnpm test:notifications

# Test MSW mocking (msw branch only)
pnpm test:msw

# Test frontend integration guide (msw branch only)
pnpm test:frontend
```

## Project Structure

```
src/
├── types/           # TypeScript type definitions
└── server.ts        # Main server file

scripts/             # Testing scripts
├── test-connection.js
├── test-notifications.js
└── test-msw.js      # MSW testing (msw branch only)
```

## Branches

- `main` - Production WebSocket server
- `msw` - Mock Service Worker testing solution (see MSW_INTEGRATION.md)

## License

UNLICENSED - Private project for Monitora application.
