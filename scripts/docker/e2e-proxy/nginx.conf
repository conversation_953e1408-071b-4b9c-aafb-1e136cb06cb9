events {
}

http {
  # Enable gzip compression
  gzip on;
  gzip_static on;
  gzip_vary on;
  gzip_min_length 256;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_buffers 32 16k;
  gzip_types
  text/plain
  text/css
  text/xml
  text/javascript
  application/json
  application/javascript
  application/xml+rss
  application/atom+xml
  image/svg+xml;

  server {
    listen 80;
    listen 443 ssl;
    server_name e2e-proxy;

    ssl_certificate /srv/certs/e2e-proxy.pem;
    ssl_certificate_key /srv/certs/e2e-proxy-key.pem;

    # Redirect HTTP to HTTPS
    if ($scheme = http) {
      return 301 https://$host$request_uri;
    }

    # Reverse Proxy Configuration for Next.js App
    location / {
      proxy_pass http://mbfe-app:3000;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
  }
}
