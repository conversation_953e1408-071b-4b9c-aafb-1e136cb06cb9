const { server } = require('../dist/mocks/node.js');

async function testMSW() {
  console.log('🚀 Starting MSW test...');
  
  // Start MSW server
  server.listen({ onUnhandledRequest: 'error' });
  console.log('✅ MSW server started');

  try {
    // Test health endpoint
    console.log('\n📊 Testing health endpoint...');
    const healthResponse = await fetch('http://localhost/health');
    const healthData = await healthResponse.json();
    console.log('Health response:', healthData);

    // Test notification endpoint
    console.log('\n📨 Testing notification endpoint...');
    const notificationData = {
      model: 'AppNotificationsLog',
      method: 'CREATE',
      data: [{
        id: 1,
        app_notification_type: 1,
        title: 'MSW Test Notification',
        icon: 'info',
        url: null,
        created: new Date().toISOString(),
        is_read: false
      }]
    };

    const notifyResponse = await fetch('http://localhost/notify', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(notificationData)
    });
    const notifyData = await notifyResponse.json();
    console.log('Notification response:', notifyData);

    // Test WebSocket connection simulation
    console.log('\n🔌 Testing WebSocket connection simulation...');
    const connectResponse = await fetch('http://localhost/ws/connect', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ clientId: 'test-client-1' })
    });
    const connectData = await connectResponse.json();
    console.log('Connect response:', connectData);

    // Test message history
    console.log('\n📜 Testing message history...');
    const messagesResponse = await fetch('http://localhost/messages');
    const messagesData = await messagesResponse.json();
    console.log('Messages response:', messagesData);

    console.log('\n✅ All MSW tests passed!');

  } catch (error) {
    console.error('❌ MSW test failed:', error);
  } finally {
    // Stop MSW server
    server.close();
    console.log('🛑 MSW server stopped');
  }
}

// Run the test
testMSW();
