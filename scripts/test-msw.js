// Simple MSW test without requiring compiled files
const { setupServer } = require('msw/node');
const { http, HttpResponse } = require('msw');

// Mock WebSocket message store
let connectedClients = [];
let messageHistory = [];

// Define handlers inline
const handlers = [
  // Health check endpoint
  http.get('http://example.com/health', () => {
    return HttpResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      connections: connectedClients.length,
    });
  }),

  // Send notification endpoint
  http.post('http://example.com/notify', async ({ request }) => {
    const body = await request.json();

    if (!body.model || !body.method || !body.data) {
      return HttpResponse.json(
        { error: 'Missing required fields: model, method, data' },
        { status: 400 }
      );
    }

    // Store message in history
    messageHistory.push(body);

    // Simulate WebSocket broadcast
    console.log(`[MSW] Broadcasting message to ${connectedClients.length} clients:`, body);

    return HttpResponse.json({
      success: true,
      message: 'Notification sent successfully',
    });
  }),

  // Mock WebSocket connection endpoint (for testing)
  http.post('http://example.com/ws/connect', async ({ request }) => {
    const { clientId } = await request.json();

    if (!connectedClients.includes(clientId)) {
      connectedClients.push(clientId);
    }

    return HttpResponse.json({
      success: true,
      clientId,
      totalConnections: connectedClients.length,
    });
  }),

  // Get message history (for testing)
  http.get('http://example.com/messages', () => {
    return HttpResponse.json({
      messages: messageHistory,
      count: messageHistory.length,
    });
  }),
];

const server = setupServer(...handlers);

async function testMSW() {
  console.log('🚀 Starting MSW test...');
  
  // Start MSW server
  server.listen({ onUnhandledRequest: 'warn' });
  console.log('✅ MSW server started');

  try {
    // Test health endpoint
    console.log('\n📊 Testing health endpoint...');
    const healthResponse = await fetch('http://example.com/health');
    const healthData = await healthResponse.json();
    console.log('Health response:', healthData);

    // Test notification endpoint
    console.log('\n📨 Testing notification endpoint...');
    const notificationData = {
      model: 'AppNotificationsLog',
      method: 'CREATE',
      data: [{
        id: 1,
        app_notification_type: 1,
        title: 'MSW Test Notification',
        icon: 'info',
        url: null,
        created: new Date().toISOString(),
        is_read: false
      }]
    };

    const notifyResponse = await fetch('http://example.com/notify', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(notificationData)
    });
    const notifyData = await notifyResponse.json();
    console.log('Notification response:', notifyData);

    // Test WebSocket connection simulation
    console.log('\n🔌 Testing WebSocket connection simulation...');
    const connectResponse = await fetch('http://example.com/ws/connect', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ clientId: 'test-client-1' })
    });
    const connectData = await connectResponse.json();
    console.log('Connect response:', connectData);

    // Test message history
    console.log('\n📜 Testing message history...');
    const messagesResponse = await fetch('http://example.com/messages');
    const messagesData = await messagesResponse.json();
    console.log('Messages response:', messagesData);

    console.log('\n✅ All MSW tests passed!');

  } catch (error) {
    console.error('❌ MSW test failed:', error);
  } finally {
    // Stop MSW server
    server.close();
    console.log('🛑 MSW server stopped');
  }
}

// Run the test
testMSW();
