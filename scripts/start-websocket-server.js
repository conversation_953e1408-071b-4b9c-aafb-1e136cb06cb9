#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')

console.log('🚀 Starting MediaBoard WebSocket Server...')

const serverPath = path.join(__dirname, '..', 'mediaboard-ws')
const serverProcess = spawn('npm', ['run', 'dev'], {
  cwd: serverPath,
  stdio: 'inherit',
  shell: true
})

serverProcess.on('error', (error) => {
  console.error('❌ Failed to start WebSocket server:', error)
  process.exit(1)
})

serverProcess.on('close', (code) => {
  console.log(`📡 WebSocket server exited with code ${code}`)
  process.exit(code)
})

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down WebSocket server...')
  serverProcess.kill('SIGINT')
})

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down WebSocket server...')
  serverProcess.kill('SIGTERM')
})
