#!/usr/bin/env node

/**
 * Test script for mitmproxy WebSocket mocking
 * 
 * This script validates the mitmproxy WebSocket mock implementation
 * and provides testing instructions.
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 mitmproxy WebSocket Mock Test')
console.log('================================\n')

// Validate the mock script exists
const mockScriptPath = path.join(__dirname, 'mitmproxy', 'mock_websocket.py')

if (!fs.existsSync(mockScriptPath)) {
    console.error('❌ Error: mock_websocket.py not found')
    console.error(`Expected location: ${mockScriptPath}`)
    process.exit(1)
}

console.log('✅ Mock script found: scripts/mitmproxy/mock_websocket.py')

// Read and validate the script content
const scriptContent = fs.readFileSync(mockScriptPath, 'utf8')

const requiredFunctions = [
    'def load(',
    'def request(',
    'def websocket_start(',
    'def websocket_end(',
    'async def send_periodic_notifications(',
    'def websocket_message('
]

console.log('\n🔍 Validating script structure:')
requiredFunctions.forEach(func => {
    if (scriptContent.includes(func)) {
        console.log(`✅ ${func.replace('def ', '').replace('async def ', '').replace('(', '')} function found`)
    } else {
        console.log(`❌ ${func.replace('def ', '').replace('async def ', '').replace('(', '')} function missing`)
    }
})

// Check for required imports
const requiredImports = [
    'from mitmproxy import ctx, http',
    'import asyncio',
    'import json',
    'import logging'
]

console.log('\n📦 Validating imports:')
requiredImports.forEach(imp => {
    if (scriptContent.includes(imp)) {
        console.log(`✅ ${imp}`)
    } else {
        console.log(`❌ Missing: ${imp}`)
    }
})

// Check for mock notification data
if (scriptContent.includes('MOCK_NOTIFICATIONS')) {
    console.log('✅ Mock notification data found')
} else {
    console.log('❌ Mock notification data missing')
}

console.log('\n📋 Testing Instructions:')
console.log('========================')

console.log('\n1. Copy to monitora-frontend:')
console.log('   mkdir -p /path/to/monitora-frontend/scripts/mitmproxy')
console.log('   cp scripts/mitmproxy/mock_websocket.py /path/to/monitora-frontend/scripts/mitmproxy/')

console.log('\n2. Add to monitora-frontend package.json scripts:')
console.log('   "mitm:ws": "mitmproxy --mode reverse:http://localhost:3000 -s scripts/mitmproxy/mock_websocket.py"')

console.log('\n3. Test the mitmproxy script:')
console.log('   cd /path/to/monitora-frontend')
console.log('   pnpm mitm:ws')
console.log('   # Should start mitmproxy on port 8080')

console.log('\n4. Configure browser proxy:')
console.log('   - Set HTTP proxy to localhost:8080')
console.log('   - Install mitmproxy CA certificate if needed')
console.log('   - Visit http://mitm.it for certificate installation')

console.log('\n5. Test WebSocket interception:')
console.log('   - Start monitora-frontend: pnpm dev')
console.log('   - Open browser with proxy configured')
console.log('   - Navigate to http://localhost:3000')
console.log('   - Check mitmproxy console for WebSocket upgrade requests')

console.log('\n6. Verify mock notifications:')
console.log('   - WebSocket connections to localhost:8080/ws should be intercepted')
console.log('   - Mock notifications should appear every 10 seconds')
console.log('   - Check browser console and app notifications')

console.log('\n🔧 Customization Options:')
console.log('=========================')
console.log('- Change notification interval: --set ws_interval=5')
console.log('- Example: pnpm mitm:ws --set ws_interval=5')
console.log('- Edit MOCK_NOTIFICATIONS in the script for custom messages')

console.log('\n🐛 Troubleshooting:')
console.log('==================')
console.log('- If WebSocket not intercepted: Check proxy configuration')
console.log('- If no notifications: Verify WebSocket URL matches localhost:8080/ws')
console.log('- If certificate errors: Install mitmproxy CA certificate')
console.log('- If script errors: Check mitmproxy version compatibility')

console.log('\n✅ Test validation complete!')
console.log('Follow the testing instructions above to verify the implementation.')
