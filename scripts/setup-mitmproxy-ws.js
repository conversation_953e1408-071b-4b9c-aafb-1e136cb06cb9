#!/usr/bin/env node

/**
 * Setup script for mitmproxy WebSocket mocking integration
 * 
 * This script copies the mitmproxy WebSocket mock script to monitora-frontend
 * and provides integration instructions.
 */

const fs = require('fs')
const path = require('path')

console.log('🔧 mitmproxy WebSocket Mock Setup')
console.log('=================================\n')

// Check if we're in the correct directory
const currentDir = process.cwd()
const packageJsonPath = path.join(currentDir, 'package.json')

if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ Error: package.json not found. Please run this script from the project root.')
    process.exit(1)
}

const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))

if (packageJson.name !== 'mediaboard-ws') {
    console.error('❌ Error: This script should be run from the mediaboard-ws project.')
    process.exit(1)
}

console.log('📋 Integration Steps for monitora-frontend:')
console.log('==========================================\n')

console.log('1. Copy the mitmproxy script to monitora-frontend:')
console.log('   cp scripts/mitmproxy/mock_websocket.py /path/to/monitora-frontend/scripts/mitmproxy/')
console.log('')

console.log('2. Add mitmproxy WebSocket command to monitora-frontend package.json:')
console.log('   Add this to the "scripts" section:')
console.log('   "mitm:ws": "mitmproxy --mode reverse:http://localhost:3000 -s scripts/mitmproxy/mock_websocket.py"')
console.log('')

console.log('3. Usage in monitora-frontend:')
console.log('   Terminal 1: pnpm mitm:ws')
console.log('   Terminal 2: pnpm dev')
console.log('   Terminal 3: Configure browser proxy to localhost:8080')
console.log('')

console.log('4. Browser Configuration:')
console.log('   - Set HTTP proxy to localhost:8080')
console.log('   - Install mitmproxy certificate if needed')
console.log('   - Navigate to http://localhost:3000')
console.log('')

console.log('5. Expected Behavior:')
console.log('   - WebSocket connections to localhost:8080/ws will be mocked')
console.log('   - Mock notifications will appear every 10 seconds (configurable)')
console.log('   - No changes to frontend code required')
console.log('')

console.log('6. Customization Options:')
console.log('   - Change notification interval: --set ws_interval=5')
console.log('   - Example: pnpm mitm:ws --set ws_interval=5')
console.log('')

console.log('📁 Files to copy:')
console.log('================')
console.log('Source: scripts/mitmproxy/mock_websocket.py')
console.log('Target: monitora-frontend/scripts/mitmproxy/mock_websocket.py')
console.log('')

console.log('🔍 Verification:')
console.log('===============')
console.log('1. Start mitmproxy: pnpm mitm:ws')
console.log('2. Configure browser proxy to localhost:8080')
console.log('3. Open monitora-frontend in browser')
console.log('4. Check browser console for WebSocket connection')
console.log('5. Verify mock notifications appear in the app')
console.log('')

console.log('✅ Setup complete! Follow the integration steps above.')
