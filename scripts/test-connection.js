#!/usr/bin/env node

/**
 * WebSocket Connection Test Script
 * 
 * This script tests WebSocket connections by:
 * 1. Connecting to the WebSocket server
 * 2. Listening for messages
 * 3. Sending test messages
 * 4. Monitoring connection status
 * 
 * Usage:
 *   npm run test:connection
 *   node scripts/test-connection.js
 */

const WebSocket = require('ws');
const http = require('http');

const WEBSOCKET_URL = 'ws://localhost:8080/ws';
const SERVER_URL = 'http://localhost:8080';

/**
 * Check server health
 */
function checkServerHealth() {
  return new Promise((resolve, reject) => {
    const req = http.request({
      hostname: 'localhost',
      port: 8080,
      path: '/health',
      method: 'GET'
    }, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(body));
        } catch (e) {
          resolve({ error: 'Invalid JSON response' });
        }
      });
    });

    req.on('error', reject);
    req.end();
  });
}

/**
 * Test WebSocket connection
 */
function testWebSocketConnection() {
  return new Promise((resolve, reject) => {
    console.log('🔌 Connecting to WebSocket server...');
    console.log(`   URL: ${WEBSOCKET_URL}`);
    
    const ws = new WebSocket(WEBSOCKET_URL);
    const results = {
      connected: false,
      messagesReceived: [],
      errors: [],
      connectionTime: null
    };
    
    const startTime = Date.now();
    let timeout;

    // Set timeout for connection
    timeout = setTimeout(() => {
      console.log('❌ Connection timeout (10 seconds)');
      results.errors.push('Connection timeout');
      ws.close();
      resolve(results);
    }, 10000);

    ws.on('open', () => {
      results.connected = true;
      results.connectionTime = Date.now() - startTime;
      console.log(`✅ Connected to WebSocket server (${results.connectionTime}ms)`);
      clearTimeout(timeout);
      
      // Keep connection open for a bit to listen for messages
      setTimeout(() => {
        console.log('🔌 Closing test connection...');
        ws.close();
      }, 3000);
    });

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        console.log('📨 Received message:', message);
        results.messagesReceived.push(message);
      } catch (e) {
        console.log('📨 Received raw message:', data.toString());
        results.messagesReceived.push(data.toString());
      }
    });

    ws.on('error', (error) => {
      console.log('❌ WebSocket error:', error.message);
      results.errors.push(error.message);
      clearTimeout(timeout);
    });

    ws.on('close', (code, reason) => {
      console.log(`🔌 Connection closed (code: ${code}, reason: ${reason || 'none'})`);
      clearTimeout(timeout);
      resolve(results);
    });
  });
}

/**
 * Send test notification while connection is active
 */
async function sendTestNotificationDuringConnection() {
  console.log('\n🧪 Testing notification delivery during active connection...');
  
  // Start WebSocket connection
  const ws = new WebSocket(WEBSOCKET_URL);
  const messages = [];
  
  return new Promise((resolve) => {
    ws.on('open', () => {
      console.log('✅ WebSocket connected, sending test notification...');
      
      // Send test notification via HTTP
      const testNotification = {
        model: 'AppNotificationsLog',
        method: 'CREATE',
        data: [{
          id: Date.now(),
          type: 1,
          title: 'Connection Test Notification',
          message: 'This notification was sent during an active WebSocket connection test',
          created_at: new Date().toISOString(),
          is_read: false
        }]
      };

      const postData = JSON.stringify(testNotification);
      const req = http.request({
        hostname: 'localhost',
        port: 8080,
        path: '/notify',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        }
      }, (res) => {
        console.log(`📤 Notification sent (status: ${res.statusCode})`);
      });

      req.on('error', (error) => {
        console.log('❌ Error sending notification:', error.message);
      });

      req.write(postData);
      req.end();

      // Close connection after 2 seconds
      setTimeout(() => {
        ws.close();
      }, 2000);
    });

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        console.log('📨 Received notification via WebSocket:', message);
        messages.push(message);
      } catch (e) {
        console.log('📨 Received raw data:', data.toString());
        messages.push(data.toString());
      }
    });

    ws.on('close', () => {
      console.log('🔌 Test connection closed');
      resolve(messages);
    });

    ws.on('error', (error) => {
      console.log('❌ WebSocket error during test:', error.message);
      resolve(messages);
    });
  });
}

/**
 * Main test function
 */
async function runConnectionTests() {
  console.log('🧪 WebSocket Connection Testing');
  console.log('===============================');
  
  try {
    // Check server health first
    console.log('🏥 Checking server health...');
    const health = await checkServerHealth();
    console.log('📊 Server status:', health);
    
    if (health.error) {
      console.log('❌ Server health check failed');
      return;
    }

    console.log(`🔌 Current connections: ${health.connections}`);
    
    // Test basic WebSocket connection
    console.log('\n🔌 Testing WebSocket connection...');
    const connectionResult = await testWebSocketConnection();
    
    console.log('\n📋 Connection Test Results:');
    console.log(`   Connected: ${connectionResult.connected ? '✅' : '❌'}`);
    if (connectionResult.connectionTime) {
      console.log(`   Connection time: ${connectionResult.connectionTime}ms`);
    }
    console.log(`   Messages received: ${connectionResult.messagesReceived.length}`);
    console.log(`   Errors: ${connectionResult.errors.length}`);
    
    if (connectionResult.errors.length > 0) {
      console.log('   Error details:', connectionResult.errors);
    }

    // Test notification delivery during active connection
    if (connectionResult.connected) {
      const notificationMessages = await sendTestNotificationDuringConnection();
      
      console.log('\n📨 Notification Delivery Test:');
      console.log(`   Messages received: ${notificationMessages.length}`);
      
      if (notificationMessages.length > 0) {
        console.log('✅ Notifications are being delivered via WebSocket');
      } else {
        console.log('⚠️  No notifications received during test');
      }
    }

    // Final health check
    console.log('\n🔄 Final health check...');
    const finalHealth = await checkServerHealth();
    console.log(`🔌 Final connections: ${finalHealth.connections}`);
    
    console.log('\n🎯 Test Summary:');
    if (connectionResult.connected) {
      console.log('✅ WebSocket connection: PASS');
    } else {
      console.log('❌ WebSocket connection: FAIL');
    }
    
    console.log('\n💡 Next steps:');
    console.log('   1. Run: npm run test:notifications');
    console.log('   2. Connect frontend and test with browser console');
    console.log('   3. Check notifications in Monitora app header');

  } catch (error) {
    console.log('❌ Test failed with error:', error.message);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runConnectionTests().catch(console.error);
}

module.exports = {
  testWebSocketConnection,
  checkServerHealth,
  sendTestNotificationDuringConnection
};
