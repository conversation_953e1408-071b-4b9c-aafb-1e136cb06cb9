"""
Mock WebSocket connections for Monitora frontend development.

This script intercepts WebSocket upgrade requests to localhost:8080/ws and provides
mock responses with the expected message format: {model, method, data}.

Usage:
    mitmproxy --mode reverse:http://localhost:3000 -s scripts/mitmproxy/mock_websocket.py

Or add to package.json:
    "mitm:ws": "mitmproxy --mode reverse:http://localhost:3000 -s scripts/mitmproxy/mock_websocket.py"
"""

import asyncio
import json
import logging
import time
from typing import Dict, Set

from mitmproxy import ctx, http

# Mock notification data following Monitora's message format
MOCK_NOTIFICATIONS = [
    {
        "model": "notification",
        "method": "create",
        "data": {
            "id": 1,
            "title": "Mock Notification 1",
            "message": "This is a test notification from mitmproxy",
            "type": "info",
            "timestamp": None  # Will be set dynamically
        }
    },
    {
        "model": "notification", 
        "method": "create",
        "data": {
            "id": 2,
            "title": "Mock Alert",
            "message": "Important system alert",
            "type": "warning",
            "timestamp": None
        }
    },
    {
        "model": "notification",
        "method": "create", 
        "data": {
            "id": 3,
            "title": "Mock Error",
            "message": "Something went wrong",
            "type": "error",
            "timestamp": None
        }
    }
]

# Track active WebSocket connections
active_connections: Set[http.HTTPFlow] = set()
notification_tasks: Dict[http.HTTPFlow, asyncio.Task] = {}


def load(loader):
    """Called when the script is loaded"""
    loader.add_option(
        name="ws_interval",
        typespec=int,
        default=10,
        help="Interval in seconds between mock notifications (default: 10)",
    )


def request(flow: http.HTTPFlow) -> None:
    """Intercept WebSocket upgrade requests"""
    # Check if this is a WebSocket upgrade request to our target
    if (flow.request.headers.get("upgrade", "").lower() == "websocket" and
        flow.request.host == "localhost" and 
        flow.request.port == 8080 and
        flow.request.path == "/ws"):
        
        logging.info("Intercepting WebSocket upgrade request to localhost:8080/ws")
        
        # Create a mock WebSocket upgrade response
        flow.response = http.Response.make(
            101,  # Switching Protocols
            b"",  # Empty body for WebSocket upgrade
            {
                "Upgrade": "websocket",
                "Connection": "Upgrade", 
                "Sec-WebSocket-Accept": "mock-accept-key",
                "Sec-WebSocket-Protocol": flow.request.headers.get("Sec-WebSocket-Protocol", "")
            }
        )


def websocket_start(flow: http.HTTPFlow) -> None:
    """Called when a WebSocket connection starts"""
    if (flow.request.host == "localhost" and 
        flow.request.port == 8080 and
        flow.request.path == "/ws"):
        
        logging.info("WebSocket connection started - beginning mock notifications")
        active_connections.add(flow)
        
        # Start periodic notification task
        task = asyncio.create_task(send_periodic_notifications(flow))
        notification_tasks[flow] = task


def websocket_end(flow: http.HTTPFlow) -> None:
    """Called when a WebSocket connection ends"""
    if flow in active_connections:
        logging.info("WebSocket connection ended")
        active_connections.discard(flow)
        
        # Cancel notification task
        if flow in notification_tasks:
            notification_tasks[flow].cancel()
            del notification_tasks[flow]


async def send_periodic_notifications(flow: http.HTTPFlow) -> None:
    """Send periodic mock notifications to the WebSocket connection"""
    notification_index = 0
    
    try:
        while flow in active_connections:
            # Wait for the specified interval
            await asyncio.sleep(ctx.options.ws_interval)
            
            # Get next notification and update timestamp
            notification = MOCK_NOTIFICATIONS[notification_index % len(MOCK_NOTIFICATIONS)].copy()
            notification["data"]["timestamp"] = int(time.time() * 1000)  # Current timestamp in ms
            
            # Send the notification
            message_json = json.dumps(notification)
            logging.info(f"Sending mock notification: {notification['data']['title']}")
            
            # Inject the message into the WebSocket connection
            ctx.master.commands.call(
                "inject.websocket", 
                flow, 
                False,  # from_client=False (server to client)
                message_json.encode()
            )
            
            notification_index += 1
            
    except asyncio.CancelledError:
        logging.info("Notification task cancelled")
    except Exception as e:
        logging.error(f"Error in notification task: {e}")


def websocket_message(flow: http.HTTPFlow) -> None:
    """Handle incoming WebSocket messages (optional)"""
    if flow.websocket is not None:
        message = flow.websocket.messages[-1]
        if message.from_client:
            logging.info(f"Client sent WebSocket message: {message.content!r}")
