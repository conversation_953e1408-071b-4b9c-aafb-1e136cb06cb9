from mitmproxy import http, ctx  # pyright: ignore[reportAttributeAccessIssue]

# Simulates the Expo mobile app WebView component that loads the web application
# This script modifies the User-Agent header to make web requests appear as if they
# come from the mobile app's WebView component

# Default version for the mobile app
DEFAULT_APP_VERSION = "1.0.0"


def load(loader):
    """Called when the script is loaded"""
    loader.add_option(
        name="app_version",
        typespec=str,
        default=DEFAULT_APP_VERSION,
        help="Mobile app version to simulate (e.g., 1.0.0, 2.1.3)",
    )


def request(flow: http.HTTPFlow) -> None:
    """Inject mobile app WebView user agent to simulate Expo mobile app requests"""
    app_version = ctx.options.app_version
    mobile_webview_identifier = f"MbExpoWebView/{app_version}"

    # Get the current user agent or use empty string if not present
    current_user_agent = flow.request.headers.get("user-agent", "")

    # Only add mobile app identifier if not already present
    if mobile_webview_identifier not in current_user_agent:
        if current_user_agent:
            # Append mobile app identifier to existing user agent
            new_user_agent = f"{current_user_agent} {mobile_webview_identifier}"
        else:
            # Use mobile app identifier as the user agent if none exists
            new_user_agent = mobile_webview_identifier

        flow.request.headers["user-agent"] = new_user_agent
