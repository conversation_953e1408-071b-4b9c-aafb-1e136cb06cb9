# WebSocket Testing Scripts

This directory contains testing scripts for the WebSocket server integration with the Monitora frontend.

## Scripts

### `test-notifications.js`
Tests notification sending functionality:
- Checks server health and connection count
- Sends test notifications via HTTP POST to `/notify`
- Verifies notifications are properly formatted for Monitora frontend
- Tests all notification types (APP_NEWS, EXPORTS, WORKSPACE_ARTICLES)

**Usage:**
```bash
npm run test:notifications
# or
node scripts/test-notifications.js
```

### `test-connection.js`
Tests WebSocket connection functionality:
- Tests basic WebSocket connection to the server
- Monitors connection time and stability
- Tests real-time notification delivery during active connections
- Provides detailed connection diagnostics

**Usage:**
```bash
npm run test:connection
# or
node scripts/test-connection.js
```

## Testing Workflow

### 1. Start the WebSocket Server
```bash
npm run dev
```

### 2. Test Server Connection
```bash
npm run test:connection
```
This will verify the WebSocket server is accepting connections.

### 3. Test Notification Sending
```bash
npm run test:notifications
```
This will send test notifications and verify they're properly formatted.

### 4. Test Frontend Integration

#### Option A: Manual Browser Testing
1. Open Monitora app in browser
2. Open browser console (F12)
3. Set WebSocket URL manually:
   ```javascript
   appStore.account.setParam('websocket_url', 'ws://localhost:8080/ws')
   ```
4. Run notification tests:
   ```bash
   npm run test:notifications
   ```
5. Check for notifications in the header bell icon

#### Option B: Backend Integration
For production use, the backend's `/init/` endpoint should return:
```json
{
  "websocket_url": "ws://localhost:8080/ws"
}
```

## Notification Format

The WebSocket server expects notifications in this format:
```json
{
  "model": "AppNotificationsLog",
  "method": "CREATE", 
  "data": [{
    "id": 1,
    "type": 1,
    "title": "Notification Title",
    "message": "Notification message",
    "created_at": "2024-01-01T00:00:00.000Z",
    "is_read": false
  }]
}
```

### Notification Types
- `type: 1` - APP_NEWS (General news/updates)
- `type: 2` - EXPORTS (Export completion notifications)  
- `type: 3` - WORKSPACE_ARTICLES (New articles in workspace)

## Troubleshooting

### Server Not Running
```
❌ Server is not running or not accessible
💡 Make sure to start the server with: npm run dev
```

### No WebSocket Connections
```
⚠️  No WebSocket connections detected
```
This means the frontend is not connected. Either:
1. Set WebSocket URL manually in browser console
2. Configure backend to return `websocket_url` in `/init/` response

### Connection Timeout
```
❌ Connection timeout (10 seconds)
```
Check if:
1. Server is running on port 8080
2. No firewall blocking WebSocket connections
3. WebSocket endpoint `/ws` is accessible

## Health Check

The server provides a health endpoint at `http://localhost:8080/health`:
```json
{
  "status": "ok",
  "connections": 0,
  "uptime": 123.456
}
```

Use this to monitor:
- Server availability
- Number of active WebSocket connections
- Server uptime
