#!/usr/bin/env node

/**
 * Test script for WebSocket notifications
 * 
 * This script tests the WebSocket server by:
 * 1. Checking server health
 * 2. Sending test notifications via HTTP POST
 * 3. Verifying WebSocket connections
 * 
 * Usage:
 *   npm run test:notifications
 *   node scripts/test-notifications.js
 */

const http = require('http');

const SERVER_URL = 'http://localhost:8080';
const WEBSOCKET_URL = 'ws://localhost:8080/ws';

// Test notification data matching Monitora frontend format
const TEST_NOTIFICATIONS = [
  {
    model: 'AppNotificationsLog',
    method: 'CREATE',
    data: [{
      id: 1,
      type: 1, // APP_NEWS
      title: 'Test News Notification',
      message: 'This is a test news notification from the WebSocket server',
      created_at: new Date().toISOString(),
      is_read: false
    }]
  },
  {
    model: 'AppNotificationsLog', 
    method: 'CREATE',
    data: [{
      id: 2,
      type: 2, // EXPORTS
      title: 'Export Complete',
      message: 'Your export has been completed successfully',
      created_at: new Date().toISOString(),
      is_read: false
    }]
  },
  {
    model: 'AppNotificationsLog',
    method: 'CREATE', 
    data: [{
      id: 3,
      type: 3, // WORKSPACE_ARTICLES
      title: 'New Articles Available',
      message: 'New articles have been added to your workspace',
      created_at: new Date().toISOString(),
      is_read: false
    }]
  }
];

/**
 * Make HTTP request
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsed = body ? JSON.parse(body) : {};
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * Check server health
 */
async function checkServerHealth() {
  console.log('🏥 Checking WebSocket server health...');
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 8080,
      path: '/health',
      method: 'GET'
    });

    if (response.status === 200) {
      console.log('✅ Server is healthy');
      console.log(`📊 Server status:`, response.data);
      return response.data;
    } else {
      console.log(`❌ Server health check failed with status ${response.status}`);
      return null;
    }
  } catch (error) {
    console.log('❌ Server is not running or not accessible:', error.message);
    console.log('💡 Make sure to start the server with: npm run dev');
    return null;
  }
}

/**
 * Send test notification
 */
async function sendNotification(notification, index) {
  console.log(`\n📤 Sending test notification ${index + 1}...`);
  console.log(`   Type: ${notification.data[0].title}`);
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 8080,
      path: '/notify',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, notification);

    if (response.status === 200) {
      console.log(`✅ Notification ${index + 1} sent successfully`);
      return true;
    } else {
      console.log(`❌ Failed to send notification ${index + 1}: ${response.status}`);
      console.log('Response:', response.data);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error sending notification ${index + 1}:`, error.message);
    return false;
  }
}

/**
 * Wait for a specified time
 */
function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🧪 WebSocket Notification Testing');
  console.log('================================');
  
  // Check server health
  const health = await checkServerHealth();
  if (!health) {
    process.exit(1);
  }

  console.log(`\n🔌 Current connections: ${health.connections}`);
  
  if (health.connections === 0) {
    console.log('⚠️  No WebSocket connections detected');
    console.log('💡 To test with frontend connection:');
    console.log('   1. Open Monitora app in browser');
    console.log('   2. Open browser console');
    console.log('   3. Run: appStore.account.setParam("websocket_url", "ws://localhost:8080/ws")');
    console.log('   4. Check connections again');
  } else {
    console.log(`✅ Found ${health.connections} active connection(s)`);
  }

  console.log('\n📨 Sending test notifications...');
  
  // Send test notifications
  let successCount = 0;
  for (let i = 0; i < TEST_NOTIFICATIONS.length; i++) {
    const success = await sendNotification(TEST_NOTIFICATIONS[i], i);
    if (success) successCount++;
    
    // Wait between notifications
    if (i < TEST_NOTIFICATIONS.length - 1) {
      await wait(1000);
    }
  }

  // Final health check
  console.log('\n🔄 Final health check...');
  await wait(500);
  const finalHealth = await checkServerHealth();
  
  console.log('\n📋 Test Summary');
  console.log('===============');
  console.log(`✅ Notifications sent: ${successCount}/${TEST_NOTIFICATIONS.length}`);
  console.log(`🔌 Active connections: ${finalHealth ? finalHealth.connections : 'unknown'}`);
  
  if (successCount === TEST_NOTIFICATIONS.length) {
    console.log('🎉 All tests passed!');
    
    if (finalHealth && finalHealth.connections > 0) {
      console.log('💡 Check the frontend for new notifications in the header bell icon');
    } else {
      console.log('💡 Connect the frontend to see notifications in the UI');
    }
  } else {
    console.log('❌ Some tests failed');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  checkServerHealth,
  sendNotification,
  TEST_NOTIFICATIONS
};
