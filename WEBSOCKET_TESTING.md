# WebSocket Server Testing Guide

This guide explains how to test the new MediaBoard WebSocket server with the existing frontend.

## Quick Start

### 1. Install WebSocket Server Dependencies
```bash
cd mediaboard-ws
npm install
```

### 2. Start the WebSocket Server
From the main project directory:
```bash
npm run websocket:dev
```

Or manually:
```bash
cd mediaboard-ws
npm run dev
```

### 3. Start the Frontend
In another terminal:
```bash
npm run dev
```

### 4. Open the Test Page
Navigate to: http://localhost:3000/websocket-test

## Testing Steps

1. **Check Server Health**: Click the "Check Server Health" button to verify the WebSocket server is running
2. **Connect WebSocket**: Click "Connect to Test Server" to connect the frontend WebSocket client
3. **Send Test Notification**: Click "Send Test Notification" to broadcast a message
4. **Verify Reception**: Check that the message appears in the "Last Message" field

## What to Expect

- ✅ WebSocket connects to `ws://localhost:8080/ws`
- ✅ HTTP API accepts POST requests to `http://localhost:8080/notify`
- ✅ Messages are broadcasted to all connected WebSocket clients
- ✅ Frontend receives messages in the expected `{model, method, data}` format
- ✅ App notifications are updated when messages match the expected pattern

## Message Format

The WebSocket server uses the same message format as your existing implementation:

```json
{
  "model": "AppNotificationsLog",
  "method": "CREATE",
  "data": [
    {
      "id": 1,
      "title": "Test Notification",
      "message": "This is a test notification",
      "is_read": false,
      "app_notification_type": 1
    }
  ]
}
```

## Integration Points

The test page temporarily overrides the `websocket_url` in your account store:

```javascript
// Connect to test server
appStore.account.setParam('websocket_url', 'ws://localhost:8080/ws')

// Disconnect (reset to original)
appStore.account.setParam('websocket_url', null)
```

## Production Integration

To use the WebSocket server in production:

1. Deploy the `mediaboard-ws` server to your infrastructure
2. Update your backend API's `/init/` endpoint to return the WebSocket server URL in the `websocket_url` field
3. The existing `MntrWebSocket` component will automatically connect to the new server

## Troubleshooting

### Server Not Starting
- Make sure you're in the `mediaboard-ws` directory when running `npm install`
- Check that port 8080 is not already in use
- Verify Node.js version compatibility

### WebSocket Connection Issues
- Ensure the server is running on `localhost:8080`
- Check browser console for WebSocket connection errors
- Verify CORS settings if testing from different origins

### Messages Not Received
- Check that the WebSocket connection is established (green "Connected" status)
- Verify the message format matches the expected structure
- Look at browser console and server logs for error messages

## Server Endpoints

- **WebSocket**: `ws://localhost:8080/ws`
- **Health Check**: `GET http://localhost:8080/health`
- **Send Notification**: `POST http://localhost:8080/notify`

## Next Steps

Once testing is complete:
1. Remove the test page (`src/pages/websocket-test.tsx`)
2. Deploy the WebSocket server to your production environment
3. Update your backend to return the production WebSocket URL
4. Monitor the server logs and connection metrics
