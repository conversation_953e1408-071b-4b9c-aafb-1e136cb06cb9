import { worker } from './browser';
import { server } from './node';

/**
 * Start MSW in the appropriate environment
 */
export async function startMSW() {
  if (typeof window !== 'undefined') {
    // Browser environment
    await worker.start({
      onUnhandledRequest: 'bypass',
    });
    console.log('[MSW] Browser worker started');
  } else {
    // Node.js environment
    server.listen({
      onUnhandledRequest: 'bypass',
    });
    console.log('[MSW] Node server started');
  }
}

/**
 * Stop MSW
 */
export function stopMSW() {
  if (typeof window !== 'undefined') {
    worker.stop();
    console.log('[MSW] Browser worker stopped');
  } else {
    server.close();
    console.log('[MSW] Node server stopped');
  }
}

/**
 * Reset MSW handlers
 */
export function resetMSW() {
  if (typeof window !== 'undefined') {
    worker.resetHandlers();
  } else {
    server.resetHandlers();
  }
  console.log('[MSW] Handlers reset');
}
