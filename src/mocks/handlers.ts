import { http, HttpResponse } from 'msw';
import { WSMessage } from '../types';

// Mock WebSocket message store
let connectedClients: string[] = [];
let messageHistory: WSMessage[] = [];

export const handlers = [
  // Health check endpoint
  http.get('/health', () => {
    return HttpResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      connections: connectedClients.length,
    });
  }),

  // Send notification endpoint
  http.post('/notify', async ({ request }) => {
    const body = await request.json() as WSMessage;

    if (!body.model || !body.method || !body.data) {
      return HttpResponse.json(
        { error: 'Missing required fields: model, method, data' },
        { status: 400 }
      );
    }

    // Store message in history
    messageHistory.push(body);

    // Simulate WebSocket broadcast
    console.log(`[MSW] Broadcasting message to ${connectedClients.length} clients:`, body);

    return HttpResponse.json({
      success: true,
      message: 'Notification sent successfully',
    });
  }),

  // Mock WebSocket connection endpoint (for testing)
  http.post('/ws/connect', async ({ request }) => {
    const { clientId } = await request.json() as { clientId: string };

    if (!connectedClients.includes(clientId)) {
      connectedClients.push(clientId);
    }

    return HttpResponse.json({
      success: true,
      clientId,
      totalConnections: connectedClients.length,
    });
  }),

  // Mock WebSocket disconnect endpoint (for testing)
  http.post('/ws/disconnect', async ({ request }) => {
    const { clientId } = await request.json() as { clientId: string };

    connectedClients = connectedClients.filter(id => id !== clientId);

    return HttpResponse.json({
      success: true,
      clientId,
      totalConnections: connectedClients.length,
    });
  }),

  // Get message history (for testing)
  http.get('/messages', () => {
    return HttpResponse.json({
      messages: messageHistory,
      count: messageHistory.length,
    });
  }),
];
let connectedClients: string[] = [];
let messageHistory: WSMessage[] = [];

export const handlers = [
  // Health check endpoint
  http.get('/health', () => {
    return HttpResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      connections: connectedClients.length,
    });
  }),

  // Send notification endpoint
  http.post('/notify', async ({ request }) => {
    const body = await request.json() as WSMessage;

    if (!body.model || !body.method || !body.data) {
      return HttpResponse.json(
        { error: 'Missing required fields: model, method, data' },
        { status: 400 }
      );
    }

    // Store message in history
    messageHistory.push(body);

    // Simulate WebSocket broadcast
    console.log(`[MSW] Broadcasting message to ${connectedClients.length} clients:`, body);

    return HttpResponse.json({
      success: true,
      message: 'Notification sent successfully',
    });
  }),

  // Mock WebSocket connection endpoint (for testing)
  http.post('/ws/connect', async ({ request }) => {
    const { clientId } = await request.json() as { clientId: string };

    if (!connectedClients.includes(clientId)) {
      connectedClients.push(clientId);
    }

    return HttpResponse.json({
      success: true,
      clientId,
      totalConnections: connectedClients.length,
    });
  }),

  // Mock WebSocket disconnect endpoint (for testing)
  http.post('/ws/disconnect', async ({ request }) => {
    const { clientId } = await request.json() as { clientId: string };

    connectedClients = connectedClients.filter(id => id !== clientId);

    return HttpResponse.json({
      success: true,
      clientId,
      totalConnections: connectedClients.length,
    });
  }),

  // Get message history (for testing)
  http.get('/messages', () => {
    return HttpResponse.json({
      messages: messageHistory,
      count: messageHistory.length,
    });
  }),
];
