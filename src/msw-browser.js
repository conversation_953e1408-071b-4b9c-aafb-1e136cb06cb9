// MSW Browser Setup for Monitora Frontend Integration
// This file provides WebSocket mocking for the frontend application

import { setupWorker } from 'msw/browser'
import { http, HttpResponse } from 'msw'

// Mock WebSocket message store
let connectedClients = []
let messageHistory = []

// WebSocket simulation state
let isWebSocketMocked = false
let mockWebSocketUrl = null

// Define handlers for WebSocket server endpoints
const handlers = [
  // Health check endpoint
  http.get('*/health', () => {
    return HttpResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      connections: connectedClients.length,
    })
  }),

  // Send notification endpoint
  http.post('*/notify', async ({ request }) => {
    const body = await request.json()
    
    if (!body.model || !body.method || !body.data) {
      return HttpResponse.json(
        { error: 'Missing required fields: model, method, data' },
        { status: 400 }
      )
    }

    // Store message in history
    messageHistory.push(body)

    // Simulate WebSocket broadcast to frontend
    if (isWebSocketMocked && window.mockWebSocketInstance) {
      console.log('[MSW] Simulating WebSocket message:', body)
      
      // Trigger the onmessage handler with the notification
      const messageEvent = new MessageEvent('message', {
        data: JSON.stringify(body)
      })
      
      if (window.mockWebSocketInstance.onmessage) {
        window.mockWebSocketInstance.onmessage(messageEvent)
      }
    }

    return HttpResponse.json({
      success: true,
      message: 'Notification sent successfully',
    })
  }),

  // Mock WebSocket connection endpoint (for testing)
  http.post('*/ws/connect', async ({ request }) => {
    const { clientId } = await request.json()
    
    if (!connectedClients.includes(clientId)) {
      connectedClients.push(clientId)
    }

    return HttpResponse.json({
      success: true,
      clientId,
      totalConnections: connectedClients.length,
    })
  }),

  // Get message history (for testing)
  http.get('*/messages', () => {
    return HttpResponse.json({
      messages: messageHistory,
      count: messageHistory.length,
    })
  }),
]

// Create MSW worker
const worker = setupWorker(...handlers)

// Mock WebSocket class to intercept WebSocket connections
class MockWebSocket {
  constructor(url) {
    console.log('[MSW] Mocking WebSocket connection to:', url)
    mockWebSocketUrl = url
    isWebSocketMocked = true
    window.mockWebSocketInstance = this
    
    // Add this client to connected clients
    const clientId = `client-${Date.now()}`
    connectedClients.push(clientId)
    
    // Simulate connection opening
    setTimeout(() => {
      if (this.onopen) {
        this.onopen(new Event('open'))
      }
    }, 100)
    
    this.readyState = WebSocket.CONNECTING
    this.url = url
  }

  close() {
    console.log('[MSW] Closing mock WebSocket connection')
    this.readyState = WebSocket.CLOSED
    isWebSocketMocked = false
    window.mockWebSocketInstance = null
    connectedClients = []
    
    if (this.onclose) {
      this.onclose(new CloseEvent('close'))
    }
  }

  send(data) {
    console.log('[MSW] Mock WebSocket send:', data)
  }

  // Event handlers (set by ReconnectingWebSocket)
  onopen = null
  onmessage = null
  onclose = null
  onerror = null
}

// Copy WebSocket constants
MockWebSocket.CONNECTING = 0
MockWebSocket.OPEN = 1
MockWebSocket.CLOSING = 2
MockWebSocket.CLOSED = 3

// MSW integration functions
export const startMSW = async () => {
  try {
    await worker.start({
      onUnhandledRequest: 'bypass',
      serviceWorker: {
        url: '/mockServiceWorker.js'
      }
    })
    console.log('[MSW] Service worker started successfully')
    return true
  } catch (error) {
    console.error('[MSW] Failed to start service worker:', error)
    return false
  }
}

export const enableWebSocketMocking = () => {
  if (typeof window !== 'undefined') {
    // Store original WebSocket
    window.OriginalWebSocket = window.WebSocket
    window.OriginalReconnectingWebSocket = window.ReconnectingWebSocket
    
    // Replace WebSocket with mock
    window.WebSocket = MockWebSocket
    
    // Also replace ReconnectingWebSocket if it exists
    if (window.ReconnectingWebSocket) {
      window.ReconnectingWebSocket = class extends MockWebSocket {
        constructor(url, protocols, options) {
          super(url)
          this.protocols = protocols
          this.options = options
        }
      }
    }
    
    console.log('[MSW] WebSocket mocking enabled')
    return true
  }
  return false
}

export const disableWebSocketMocking = () => {
  if (typeof window !== 'undefined' && window.OriginalWebSocket) {
    window.WebSocket = window.OriginalWebSocket
    if (window.OriginalReconnectingWebSocket) {
      window.ReconnectingWebSocket = window.OriginalReconnectingWebSocket
    }
    
    isWebSocketMocked = false
    window.mockWebSocketInstance = null
    connectedClients = []
    
    console.log('[MSW] WebSocket mocking disabled')
    return true
  }
  return false
}

export const sendMockNotification = (notification) => {
  return fetch('/notify', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(notification)
  })
}

export const getMockStatus = () => ({
  isWebSocketMocked,
  mockWebSocketUrl,
  connectedClients: connectedClients.length,
  messageHistory: messageHistory.length
})

// Auto-start MSW in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  startMSW()
}

export { worker }
