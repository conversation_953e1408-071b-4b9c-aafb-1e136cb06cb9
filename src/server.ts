import { WebSocketServer, WebSocket } from 'ws';
import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import pino from 'pino';
import { v4 as uuidv4 } from 'uuid';

import { ServerConfig, WSMessage, ClientConnection } from './types';

// Server configuration from environment variables
const config: ServerConfig = {
  port: parseInt(process.env['PORT'] || '8080'),
  host: process.env['HOST'] || '0.0.0.0',
  corsOrigin: process.env['CORS_ORIGIN'] || 'http://localhost:3000',
  logLevel: process.env['LOG_LEVEL'] || 'info',
  logPretty: process.env['NODE_ENV'] !== 'production',
};

// Initialize logger
const logger = pino(
  config.logPretty
    ? {
        level: config.logLevel,
        transport: {
          target: 'pino-pretty',
          options: {
            colorize: true,
            translateTime: 'SYS:standard',
          },
        },
      }
    : {
        level: config.logLevel,
      }
);

// Simple connection management
const connections = new Map<string, ClientConnection>();

function addConnection(ws: WebSocket): ClientConnection {
  const connection: ClientConnection = {
    id: uuidv4(),
    ws,
    isAlive: true,
    lastPing: Date.now(),
  };

  connections.set(connection.id, connection);
  logger.info({ connectionId: connection.id }, 'New connection added');
  return connection;
}

function removeConnection(connectionId: string): void {
  const connection = connections.get(connectionId);
  if (connection) {
    connections.delete(connectionId);
    logger.info({ connectionId }, 'Connection removed');
  }
}

function broadcast(message: WSMessage): void {
  const messageStr = JSON.stringify(message);
  let sentCount = 0;

  for (const [connectionId, connection] of connections) {
    if (connection.ws.readyState === WebSocket.OPEN) {
      connection.ws.send(messageStr);
      sentCount++;
    } else {
      removeConnection(connectionId);
    }
  }

  logger.info({ sentCount, totalConnections: connections.size }, 'Message broadcasted');
}

// Create Express app
const app = express();

// Middleware
app.use(cors({
  origin: config.corsOrigin,
  credentials: true,
}));
app.use(express.json());

// Health check endpoint
app.get('/health', (_req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    connections: connections.size,
  });
});

// Send notification endpoint
app.post('/notify', (req, res) => {
  const { model, method, data } = req.body;

  if (!model || !method || !data) {
    return res.status(400).json({
      error: 'Missing required fields: model, method, data',
    });
  }

  const message: WSMessage = { model, method, data };
  broadcast(message);

  return res.json({
    success: true,
    message: 'Notification sent successfully',
  });
});

// Create HTTP server
const server = createServer(app);

// Create WebSocket server
const wss = new WebSocketServer({
  server,
  path: '/ws',
});

logger.info('Starting MediaBoard WebSocket Server');

// WebSocket connection handler
wss.on('connection', (ws: WebSocket, req) => {
  const connection = addConnection(ws);

  logger.info({
    connectionId: connection.id,
    remoteAddress: req.socket.remoteAddress
  }, 'New WebSocket connection');

  // Handle connection close
  ws.on('close', () => {
    removeConnection(connection.id);
  });

  // Handle connection error
  ws.on('error', (error) => {
    logger.error({ connectionId: connection.id, error }, 'WebSocket error');
    removeConnection(connection.id);
  });

  // Keep connection alive
  const pingInterval = setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.ping();
    } else {
      clearInterval(pingInterval);
    }
  }, 30000);

  ws.on('pong', () => {
    connection.isAlive = true;
    connection.lastPing = Date.now();
  });
});

// Cleanup stale connections
setInterval(() => {
  const now = Date.now();
  for (const [connectionId, connection] of connections) {
    if (now - connection.lastPing > 60000) { // 1 minute timeout
      logger.info({ connectionId }, 'Removing stale connection');
      removeConnection(connectionId);
    }
  }
}, 30000);

// Graceful shutdown
const gracefulShutdown = () => {
  logger.info('Shutting down gracefully');
  server.close(() => {
    process.exit(0);
  });
};

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Start server
server.listen(config.port, config.host, () => {
  logger.info({
    port: config.port,
    host: config.host
  }, 'MediaBoard WebSocket Server started');
});
