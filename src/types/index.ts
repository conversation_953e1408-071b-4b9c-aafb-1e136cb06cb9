import { WebSocket } from 'ws';

// Simple message format matching existing frontend implementation
export interface WSMessage {
  model: string;
  method: string;
  data: any[];
}

// Client connection
export interface ClientConnection {
  id: string;
  ws: WebSocket;
  isAlive: boolean;
  lastPing: number;
}

// Server configuration
export interface ServerConfig {
  port: number;
  host: string;
  corsOrigin: string;
  logLevel: string;
  logPretty: boolean;
}
