import { t } from '@lingui/core/macro'
import * as Sen<PERSON> from '@sentry/nextjs'
import cookie from 'js-cookie'
import get from 'lodash/get'
import identityFn from 'lodash/identity'
import noopFn from 'lodash/noop'
import { applySnapshot, flow, getEnv, getRoot, types } from 'mobx-state-tree'
import config from '~/config'
import events from '~/constants/gtm'
import staticFeeds from '~/constants/staticFeeds'
import { login, logout } from '~/helpers/auth'
import dataURItoBlob from '~/helpers/dataURItoBlob'
import getAppSettings from '~/helpers/getAppSettings'
import { addUserIdent, pushEvent, removeUserIdent } from '~/helpers/gtm'
import setLocale from '~/helpers/i18n'
import isUrl from '~/helpers/isUrl'
import { routerPush, routerReplace } from '~/helpers/router'
import CurrencyStoreArrItem from '~/store/models/account/enums/currency/CurrencyStoreArrItem'
import FeatureRequestsArrItem from './FeatureRequestsArrItem'
import FrontendStorageStore from './frontendStorage/FrontendStorageStore'

const UserStore = types
  .model('UserStore', {
    company_name: '',
    currency: types.safeReference(CurrencyStoreArrItem),
    email: '',
    feature_requests: types.array(FeatureRequestsArrItem),
    frontend_storage: types.optional(FrontendStorageStore, {}),
    is_active: false,
    isLoading: types.optional(types.boolean, false),
    is_2fa_walled: true,
    is_pdf_parser: false,
    is_public_access: false,
    is_publisher: false,
    is_salesman: false,
    app_language: '',
    logo_image_url: '',
    name: '',
    partner_code: '',
    phone: '',
    uuid: types.maybeNull(types.string),
  })
  .views((self) => ({
    get app() {
      return getRoot(self)
    },
    get isActiveUser() {
      return !!(
        self.isImpersonating ||
        (self.is_active && self.app.account.workspace && !self.app.account.workspace?.is_expired)
      )
    },
    get isAdmin() {
      return self.is_salesman
    },
    get isAdminingOwnUser() {
      return self.email === get(self.app.admin.customer.userDetail.user, 'email')
    },
    get isAdminingOwnWorkspace() {
      return self.app.account.workspace.id === self.app.admin.customer.workspaceDetail.workspace.id
    },
    get isImpersonating() {
      return Boolean(self.app.account.adminToken)
    },
    get isPublic() {
      return self.is_public_access
    },
  }))
  .actions((self) => ({
    init(reinit, deferSetLocale) {
      if (
        (!reinit && self.app.account.isLogged) ||
        (!self.app.account.token && !self.app.account.publicToken)
      ) {
        return false
      }

      return getEnv(self)
        .apiClient.get('/init/')
        .then(async (res) => {
          // temporary init account store
          self.app.account.updateResults(res)

          if (self.app.account.workspace?.primary_app) {
            self.app.appSettings = await getAppSettings({
              primaryApp: self.app.account.workspace.primary_app.id,
              country: res.workspace.phone_number_region,
            })
          }

          // Init Web Sockets
          process.env.NEXT_PUBLIC_WEBSOCKET_URL
            ? self.app.account.setParam('websocket_url', process.env.NEXT_PUBLIC_WEBSOCKET_URL)
            : res.websocket_url
              ? self.app.account.setParam('websocket_url', res.websocket_url)
              : null

          if (!self.isImpersonating) {
            if (
              window.location.pathname === '/user/verify-account' &&
              get(res, 'user.is_2fa_walled')
            ) {
              return
            }

            if (
              get(res, 'user.is_2fa_walled') &&
              window.location.pathname !== '/user/verify-account'
            ) {
              self.triggerVerify()
              routerReplace('/user/not-verified')
              return
            }

            if (get(res, 'workspace.is_expired')) {
              self.app.account.setParam('workspaces', res.workspaces)
              self.app.account.setParam('workspace', res.workspace)
              routerReplace('/user/expired')
              return true
            }

            if (!get(res, 'user.is_active')) {
              routerReplace('/user/inactive')
              return true
            }

            if (!get(res, 'workspace')) {
              routerReplace('/user/no-workspace')
              return true
            }
          }

          Sentry.getCurrentScope().setUser({
            email: res.user.email,
          })

          window.ReactNativeWebView?.postMessage(
            JSON.stringify(['MNTR_SENTRY_USER', { email: res.user.email }]),
          )

          Sentry.getCurrentScope().setTag('app_language', res.user.app_language)

          Sentry.getCurrentScope().setExtras({
            name: res.user.name,
            company_name: res.user.company_name,
          })

          self.app.account.setParam('user', res.user)
          self.app.account.setParam('enums', res.enums)
          self.app.account.setParam('workspace', res.workspace)
          self.app.account.setParam('workspaces', res.workspaces)
          self.app.account.setParam('intercom', res.intercom)
          self.app.account.setParam('forms_defaults', res.forms_defaults)
          self.app.topics.setParam(
            'list',
            res.topic_monitors.map((item) => {
              return {
                id: item.id,
                data: item,
              }
            }),
          )

          self.app.topics.folders = res.topic_monitor_folders

          self.app.monitoring.createExportFeed(staticFeeds.EXPORT_FEED, {
            feedId: staticFeeds.EXPORT_FEED,
            includeChart: false,
          })
          if (self.app.account.workspace.limits.export_basket_limit_used) {
            self.app.monitoring.feedMap
              .get(staticFeeds.EXPORT_FEED)
              .exportStore.setCounter(self.app.account.workspace.limits.export_basket_limit_used)
          }

          const appLanguage = get(res, 'user.app_language')

          if (!deferSetLocale && appLanguage && appLanguage !== self.app.appLanguage) {
            await setLocale(appLanguage)
          }

          // Load App Noficitions
          if (!self.isPublic) {
            self.app.appNotifications.load()
          }

          addUserIdent({
            ...res.user,
            isImpersonating: self.isImpersonating,
            isPublicAccess: self.isPublic,
            productId: self.app.appSettings.gtmProductId,
            workspaceId: res.workspace.uuid,
            primaryApp: self.app.appSettings.primaryApp,
          })

          return true
        })
        .catch((err) => {
          if (/5\d{2}/.test(err.status)) routerPush('/500')
        })
        .finally(() => {
          self.app.disableLoading()
        })
    },
    activate(query) {
      return getEnv(self)
        .apiClient.post('/accounts/activate/', {
          data: {
            token: query.token,
            uid: query.uid,
          },
        })
        .then(async (res) => {
          self.app.account.setParam('token', res.token)
          await self.init(true)
          login({ route: '/?welcome', token: res.token })
        })
        .catch(identityFn)
    },
    afterCreate() {
      if (typeof window !== 'undefined') {
        if (self.app.route.query.auth) {
          self.app.account.setParam('publicToken', self.app.route.query.auth)
        }

        if (self.app.route.query.dashboardKey) {
          self.app.account.setParam('publicToken', `Dashboard ${self.app.route.query.dashboardKey}`)
        }

        if (!self.app.account.token) {
          const tokenString = cookie.get('monitora-token')

          if (tokenString) {
            const tokens = tokenString.split(',')

            self.app.account.setParam('token', tokens[0])
            self.app.account.setParam('adminToken', tokens[1])
          }
        }
      }
    },
    autologin({ token }) {
      return self.app.apiClient
        .post('/accounts/autologin/', {
          data: { token },
        })
        .then(async (res) => {
          self.app.account.setParam('token', res.token)

          if (await self.init(true)) {
            if (!self.app.account.workspace?.is_expired) {
              login({ route: '/?autologin', token: res.token })
            }
          }
        })
        .catch(identityFn)
    },
    changePassword(data) {
      return getEnv(self)
        .apiClient.post('/accounts/password/change/', {
          data,
          displayNotifications: false,
        })
        .then((res) => {
          login({ token: res.token })
          self.app.account.setParam('token', res.token)

          self.app.notification.add(t`Your password has been changed successfully.`, 'success')
          return res
        })
        .catch((errors) => {
          return errors
        })
    },
    // confirm marketing campaign activation
    confirmCampaign: flow(
      /**
       * @param {String} campaign name
       * @return {Promise} resolves data
       */
      function* confirmCampaign(campaign, email) {
        let url

        switch (campaign) {
          case 'yoy-analysis':
            url = '/campaign/yoy-analysis/'
            break
          case 'reactivate-24':
            url = '/campaign/reactivate-24/'
            break
          default:
            url = null
        }

        if (!url) {
          return Promise.reject()
        }

        try {
          const res = yield getEnv(self).apiClient.post(url, { data: { email } })
          return res
        } catch (err) {
          return err
        }
      },
    ),
    impersonate(userId) {
      return getEnv(self)
        .apiClient.post(`/staff/user/${userId}/login-as/`)
        .then(async (res) => {
          self.app.reset(true)
          const adminToken = self.isImpersonating
            ? self.app.account.adminToken
            : self.app.account.token
          cookie.set('monitora-after-unimpersonate-route', self.app.route.asPath)
          self.app.account.setParam('adminToken', adminToken)
          self.app.account.setParam('token', res.token)
          await self.init(true)
          login({ route: '/', token: [res.token, adminToken].join(',') })
        })
        .catch(identityFn)
    },
    impersonateWorkspace(workspaceId) {
      return getEnv(self)
        .apiClient.post(`/staff/workspace/${workspaceId}/login-as/`)
        .then(async (res) => {
          self.app.reset(true)
          const adminToken = self.isImpersonating
            ? self.app.account.adminToken
            : self.app.account.token
          cookie.set('monitora-after-unimpersonate-route', self.app.route.asPath)
          self.app.account.setParam('adminToken', adminToken)
          self.app.account.setParam('token', res.token)
          await self.init(true)
          login({ route: '/', token: [res.token, adminToken].join(',') })
        })
        .catch(identityFn)
    },
    async unimpersonate() {
      const adminToken = self.app.account.adminToken
      self.app.reset(true)
      self.app.account.setParam('token', adminToken)
      self.app.account.setParam('adminToken', null)
      await self.init(true)

      login({
        route: cookie.get('monitora-after-unimpersonate-route') || '/',
        token: adminToken,
      })

      cookie.remove('monitora-after-unimpersonate-route')
    },
    login(model) {
      self.markLoading(true)

      return getEnv(self)
        .apiClient.post('/accounts/login/', {
          data: { ...model },
          displayNotifications: false,
        })
        .then(async (res) => {
          self.app.account.setParam('token', res.token)

          const {
            pathname,
            query: { pal: primaryAppLanguage, ...query },
          } = self.app.route

          if (
            self.app.appSettings.defaultAppLanguage !== self.app.appLanguage &&
            !config.locales[primaryAppLanguage]
          ) {
            await self.saveLanguage()
          }

          login({
            route: {
              pathname,
              query,
            },
            token: res.token,
          })
        })
        .catch((errors) => {
          return errors
        })
        .finally(() => {
          self.markLoading(false)
        })
    },
    logout(route) {
      return self.app.apiClient
        .post('/accounts/logout/', { defaultErrorHandler: false })
        .catch(identityFn)
        .finally(() => {
          logout(route)
          self.app.reset()
          removeUserIdent()
        })
    },
    markLoading(loading) {
      self.isLoading = loading
    },
    newPassword(data) {
      self.isLoading = true

      return getEnv(self)
        .apiClient.post('/accounts/password/reset/step2/', {
          data,
          displayNotifications: false,
        })
        .then((res) => {
          self.isLoading = false
          self.app.account.setParam('token', res.token)
          login({ route: '/', token: res.token })
        })
        .catch((errors) => {
          self.isLoading = false
          return errors
        })
    },
    resetPassword(data, callback, displayNotifications = false) {
      self.isLoading = true

      return getEnv(self)
        .apiClient.post('/accounts/password/reset/step1/', {
          data,
          displayNotifications,
        })
        .then(() => {
          callback?.()
        })
        .catch((errors) => {
          return errors
        })
        .finally(() => {
          self.isLoading = false
        })
    },
    saveLanguage() {
      pushEvent(events.LANGUAGE_CHANGED)

      return self.app.apiClient
        .patch('/accounts/user/', {
          data: { app_language: self.app.appLanguage },
        })
        .catch(identityFn)
    },
    setParam(param, value) {
      self[param] = value
    },
    signUp(data) {
      self.isLoading = true

      return getEnv(self)
        .apiClient.post('/accounts/register/step1/', {
          data: {
            ...data,
            primary_app: self.app.appSettings.primaryApp,
            utm: self.app.session.utm,
          },
          displayNotifications: false,
        })
        .then((res) => {
          self.app.account.setParam('signUpCompletion', res)
          routerPush({ pathname: '/sign-up-completion', query: self.app.route.query })
          pushEvent('signup_step1')
        })
        .catch((errors) => {
          return errors
        })
        .finally(() => {
          self.isLoading = false
        })
    },
    signUpAdmin(data) {
      self.isLoading = true

      return getEnv(self)
        .apiClient.post('/staff/register/step1/', { data, displayNotifications: false })
        .then((res) => {
          self.app.account.setParam('signUpCompletion', res)
          routerPush('/staff/sign-up-completion')
        })
        .catch((errors) => {
          return errors
        })
        .finally(() => {
          self.isLoading = false
        })
    },
    signUpAdminSecond(data) {
      data.dry_run = false
      data.app_language = self.app.appLanguage
      self.isLoading = true

      return getEnv(self)
        .apiClient.post('/staff/register/step2/', { data, displayNotifications: false })
        .then(async (res) => {
          cookie.set('monitora-after-unimpersonate-route', '/staff/admin/customers')
          self.app.account.setParam('adminToken', self.app.account.token)
          self.app.account.setParam('token', res.token)
          await self.init(true)
          login({ route: '/', token: [res.token, self.app.account.adminToken].join(',') })
        })
        .catch((errors) => {
          return errors
        })
        .finally(() => {
          self.isLoading = false
        })
    },
    companyAutocomplete(query, country_code) {
      if (query) {
        return getEnv(self)
          .apiClient.get('/accounts/register/company-autocomplete/', {
            query: {
              country_code,
              query,
            },
          })
          .then((res) => {
            self.app.account.setParam('companyAutocompleteList', res.companies)
          })
          .catch(identityFn)
      }
    },
    signUpSecond(data) {
      data.dry_run = false
      data.app_language = self.app.appLanguage
      self.isLoading = true

      return getEnv(self)
        .apiClient.post('/accounts/register/step2/', {
          data: {
            ...data,
            primary_app: self.app.appSettings.primaryApp,
            utm: self.app.session.utm,
          },
          displayNotifications: false,
        })
        .then((res) => {
          self.app.account.setParam('token', res.token)
          pushEvent('sign_up')
          login({ isImmediatelyAfterSignUp: true, route: '/?welcome', token: res.token })
        })
        .catch((errors) => {
          return errors
        })
        .finally(() => {
          self.isLoading = false
        })
    },
    switchWorkspace(workspace_uuid) {
      return self.app.apiClient
        .post(`/workspaces/switch/`, {
          data: { workspace_uuid },
        })
        .then(() => {
          location.pathname = '/'
        })
        .catch(identityFn)
    },
    triggerVerify(force = false) {
      return getEnv(self)
        .apiClient.post('/accounts/verify/trigger/', { data: { force } })
        .catch(noopFn)
    },
    verify(uuid) {
      self.app.markLoading(true)

      if (!uuid) {
        return self.app.markLoading(false)
      }

      return getEnv(self)
        .apiClient.post('/accounts/verify/', { data: { uuid }, defaultErrorHandler: false })
        .then(() => {
          window.location.href = '/'
        })
        .catch(() => {
          return self.app.markLoading(false)
        })
    },
    update(data) {
      let files

      if (data.logo_image && !isUrl(data.logo_image)) {
        files = [
          {
            param: 'logo_image',
            value: dataURItoBlob(data.logo_image),
          },
        ]
      }

      return getEnv(self)
        .apiClient.patch('/accounts/user/', {
          files,
          data,
        })
        .then((res) => {
          self.updateResults(res)
          pushEvent(events.SETTINGS_EDITED)
        })
        .catch(noopFn)
    },
    updateResults(res) {
      try {
        applySnapshot(self, { ...self, ...res })
      } catch (error) {
        getEnv(self).captureException(error)
      }
    },
  }))

export default UserStore
