import { useState } from 'react'
import { observer } from '~/helpers/mst'
import { ObservedFC } from '~/helpers/msts'

const WebSocketTest: ObservedFC = ({ appStore }) => {
  const [testMessage, setTestMessage] = useState({
    model: 'AppNotificationsLog',
    method: 'CREATE',
    data: [
      {
        id: Date.now(),
        title: 'Test Notification',
        message: 'This is a test notification from WebSocket server',
        is_read: false,
        app_notification_type: 1,
      },
    ],
  })

  const [serverUrl, setServerUrl] = useState('http://localhost:8080')
  const [isConnected, setIsConnected] = useState(false)

  const sendTestNotification = async () => {
    try {
      const response = await fetch(`${serverUrl}/notify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testMessage),
      })

      if (response.ok) {
        console.log('Test notification sent successfully')
      } else {
        console.error('Failed to send test notification')
      }
    } catch (error) {
      console.error('Error sending test notification:', error)
    }
  }

  const connectToTestServer = () => {
    // Temporarily override the websocket_url for testing
    appStore.account.setParam('websocket_url', `ws://localhost:8080/ws`)
    setIsConnected(true)
  }

  const disconnectFromTestServer = () => {
    // Reset to original websocket_url (null in this case)
    appStore.account.setParam('websocket_url', null)
    setIsConnected(false)
  }

  const checkServerHealth = async () => {
    try {
      const response = await fetch(`${serverUrl}/health`)
      const data = await response.json()
      console.log('Server health:', data)
      alert(`Server Status: ${data.status}\nConnections: ${data.connections}`)
    } catch (error) {
      console.error('Error checking server health:', error)
      alert('Failed to connect to WebSocket server. Make sure it is running on localhost:8080')
    }
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>WebSocket Server Test</h1>
      
      <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h3>Server Connection</h3>
        <p>
          <strong>Current WebSocket URL:</strong>{' '}
          {appStore.account.websocket_url || 'Not connected'}
        </p>
        <p>
          <strong>Last Message:</strong>{' '}
          {appStore.websocketMessages.lastMessage ? 
            JSON.stringify(appStore.websocketMessages.lastMessage, null, 2) : 
            'No messages received'
          }
        </p>
        
        <div style={{ marginTop: '10px' }}>
          <button 
            onClick={checkServerHealth}
            style={{ marginRight: '10px', padding: '8px 16px' }}
          >
            Check Server Health
          </button>
          
          {!isConnected ? (
            <button 
              onClick={connectToTestServer}
              style={{ marginRight: '10px', padding: '8px 16px', backgroundColor: '#4CAF50', color: 'white' }}
            >
              Connect to Test Server
            </button>
          ) : (
            <button 
              onClick={disconnectFromTestServer}
              style={{ marginRight: '10px', padding: '8px 16px', backgroundColor: '#f44336', color: 'white' }}
            >
              Disconnect from Test Server
            </button>
          )}
        </div>
      </div>

      <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h3>Send Test Notification</h3>
        <p>This will send a notification through the HTTP API that gets broadcasted to all WebSocket clients.</p>
        
        <div style={{ marginBottom: '10px' }}>
          <label>
            Server URL:
            <input
              type="text"
              value={serverUrl}
              onChange={(e) => setServerUrl(e.target.value)}
              style={{ marginLeft: '10px', padding: '5px', width: '200px' }}
            />
          </label>
        </div>

        <div style={{ marginBottom: '10px' }}>
          <label>
            Test Message:
            <textarea
              value={JSON.stringify(testMessage, null, 2)}
              onChange={(e) => {
                try {
                  setTestMessage(JSON.parse(e.target.value))
                } catch (error) {
                  // Invalid JSON, ignore
                }
              }}
              style={{ 
                marginLeft: '10px', 
                padding: '5px', 
                width: '400px', 
                height: '150px',
                fontFamily: 'monospace'
              }}
            />
          </label>
        </div>

        <button 
          onClick={sendTestNotification}
          style={{ padding: '8px 16px', backgroundColor: '#2196F3', color: 'white' }}
        >
          Send Test Notification
        </button>
      </div>

      <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h3>Instructions</h3>
        <ol>
          <li>Make sure the WebSocket server is running: <code>cd mediaboard-ws && npm run dev</code></li>
          <li>Click "Check Server Health" to verify the server is running</li>
          <li>Click "Connect to Test Server" to connect the WebSocket client</li>
          <li>Click "Send Test Notification" to send a test message</li>
          <li>Watch the "Last Message" field above to see received messages</li>
        </ol>
        
        <h4>Expected Behavior:</h4>
        <ul>
          <li>The WebSocket should connect to ws://localhost:8080/ws</li>
          <li>Sending a notification should broadcast it to all connected clients</li>
          <li>The message should appear in the "Last Message" field</li>
          <li>App notifications should be updated if the message matches the expected format</li>
        </ul>
      </div>

      <div style={{ padding: '15px', border: '1px solid #ccc', borderRadius: '5px', backgroundColor: '#f9f9f9' }}>
        <h3>Recent WebSocket Messages</h3>
        <pre style={{ fontSize: '12px', maxHeight: '200px', overflow: 'auto' }}>
          {appStore.websocketMessages.messages.length > 0 
            ? appStore.websocketMessages.messages.slice(-5).map((msg, index) => 
                `${index + 1}. ${JSON.stringify(msg, null, 2)}\n`
              ).join('\n')
            : 'No messages received yet'
          }
        </pre>
      </div>
    </div>
  )
}

export default observer(WebSocketTest)
