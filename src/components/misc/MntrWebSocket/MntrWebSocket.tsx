import { useEffect, useRef } from 'react'
import ReconnectingWebSocket from 'reconnecting-websocket'

interface IMntrWebSocketProps {
  websocketUrl: string
  onMessage: (data: any) => void
}

export default function MntrWebSocket({ websocketUrl, onMessage }: IMntrWebSocketProps) {
  const ws = useRef<ReconnectingWebSocket | null>(null)

  useEffect(() => {
    if (websocketUrl) {
      ws.current = new ReconnectingWebSocket(websocketUrl)

      ws.current.onopen = () => {
        console.log('[MntrWebSocket] opened')
      }

      ws.current.onmessage = (event) => {
        onMessage(JSON.parse(event.data))
      }

      ws.current.onclose = () => {
        console.log('[MntrWebSocket] closed')
      }
    }

    return () => {
      ws.current?.close()
    }
  }, [onMessage, websocketUrl])

  return null
}
