import { useEffect } from 'react'

const useThumbnailWheelScroll = (ref) => {
  useEffect(() => {
    const handleScroll = (event) => {
      event.preventDefault()
      event.stopPropagation()
      if (ref.current) {
        if (Math.abs(event.deltaX) > Math.abs(event.deltaY)) {
          ref.current.scrollLeft += event.deltaX
        } else {
          ref.current.scrollLeft += event.deltaY
        }
      }
    }

    const element = ref.current
    if (element) {
      element.addEventListener('wheel', handleScroll)
      return () => {
        element.removeEventListener('wheel', handleScroll)
      }
    }
  }, [ref])
}

export default useThumbnailWheelScroll
