import { t } from '@lingui/core/macro'
import { useState } from 'react'
import { Field } from 'react-final-form'
import { styled } from 'styled-components'

import identityFn from 'lodash/identity'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import { Box } from '~/components/misc/Mntr'
import MntrForm, { IFormSchemaItem, IMntrFormProps } from '~/components/misc/MntrForm/MntrForm'
import EmbedFacebook from '~/components/monitoring/Inspector/InspectorSource/MediaView/EmbedFacebook/EmbedFacebook'
import EmbedInstagram from '~/components/monitoring/Inspector/InspectorSource/MediaView/EmbedInstagram/EmbedInstagram'
import EmbedTwitter from '~/components/monitoring/Inspector/InspectorSource/MediaView/EmbedTwitter/EmbedTwitter'
import { embedTypes } from '~/constants/embedTypes'
import { convertVimeoUrlToEmbedUrl } from '~/helpers/convertVimeoUrl'
import { convertYoutubeUrlToEmbedUrl } from '~/helpers/convertYoutubeUrl'

const YoutubeIframe = styled.iframe`
  aspect-ratio: 16 / 9;
  width: 100%;
  max-width: 600px;
  display: block;
`

const VimeoContainer = styled(Box)`
  padding: 56.25% 0 0 0;
  position: relative;
`

const VimeoIframe = styled.iframe`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
`

const StickyUrlField = styled(Box)`
  position: sticky;
  top: -${({ theme }) => theme.space[3]}px;
  z-index: 10;
  background-color: ${({ theme }) => theme.paper.background};
`

interface IFormEditorEmbedUrlProps extends IMntrFormProps {
  type: string
  isEdit: boolean
  placeholderUrl: string
}

const FormEditorEmbedUrl = ({
  onSubmit,
  initialValues,
  validate,
  type,
  isEdit,
  placeholderUrl,
}: IFormEditorEmbedUrlProps) => {
  const [isVisiblePreview, setIsVisiblePreview] = useState(false)
  const formSchema: IFormSchemaItem[] = [
    {
      customComponent: () => {
        return (
          <StickyUrlField mt={-3} pt={3}>
            <Field
              autoFocus
              name="url"
              fullWidth
              placeholder={`${t`For example`}: ${placeholderUrl}`}
              parse={identityFn}
              // @ts-expect-error TODO refactor adapters to tsx
              component={MntrTextFieldAdapter}
            />
          </StickyUrlField>
        )
      },
    },
    {
      customComponent: ({ values }) => {
        return (
          <Box mt={1}>
            {isVisiblePreview && (
              <>
                {type === embedTypes.EMBED_TYPE_TWITTER && (
                  <EmbedTwitter item={{ url: values.url }} />
                )}
                {type === embedTypes.EMBED_TYPE_FACEBOOK && (
                  <EmbedFacebook item={{ url: values.url }} viewportWidth={580} />
                )}

                {type === embedTypes.EMBED_TYPE_YOUTUBE && (
                  <YoutubeIframe
                    src={convertYoutubeUrlToEmbedUrl(values.url)}
                    frameBorder="0"
                    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  />
                )}

                {type === embedTypes.EMBED_TYPE_INSTAGRAM && (
                  <EmbedInstagram item={{ url: values.url }} />
                )}
                {type === embedTypes.EMBED_TYPE_VIMEO && (
                  <VimeoContainer>
                    <VimeoIframe
                      frameBorder="0"
                      src={convertVimeoUrlToEmbedUrl(values.url)}
                      allow="autoplay; fullscreen; picture-in-picture"
                      allowFullScreen
                    />
                  </VimeoContainer>
                )}
              </>
            )}
          </Box>
        )
      },
    },
    {
      actions: ({ values }) => {
        return [
          {
            bg: 'secondary',
            rounded: true,
            label: isEdit ? t`Edit` : t`Insert`,
            type: 'submit',
            disabled: !values.url,
          },
        ]
      },
    },
  ]
  return (
    <MntrForm
      contentPadding={3}
      schema={formSchema}
      onSubmit={onSubmit}
      onChange={({ values }) => {
        setIsVisiblePreview(Boolean(values.url))
      }}
      validate={validate}
      initialValues={initialValues}
    />
  )
}

export default FormEditorEmbedUrl
