import { useEffect, useRef, useState } from 'react'

const HtmlCodeIframeView = ({ value = '' }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null) as React.RefObject<HTMLIFrameElement | null>
  const [height, setHeight] = useState(0)

  useEffect(() => {
    const updateHeight = () => {
      setTimeout(() => {
        if (iframeRef.current) {
          setHeight(iframeRef.current.contentWindow?.document.body?.scrollHeight || 0)
        }
      }, 40)
    }

    // Trigger updateHeight when the content in the iframe loads
    const iframeLoadHandler = () => {
      updateHeight()
    }

    const iframe = iframeRef.current
    if (iframe) {
      iframe.addEventListener('load', iframeLoadHandler)
    }

    window.addEventListener('resize', updateHeight)

    return () => {
      if (iframe) {
        iframe.removeEventListener('load', iframeLoadHandler)
      }
      window.removeEventListener('resize', updateHeight)
    }
  }, [value])
  // content height + iframe offset
  const iframeHeight = height + 16

  return (
    <iframe
      ref={iframeRef}
      srcDoc={value}
      width="100%"
      height={`${iframeHeight}px`}
      scrolling="no"
      frameBorder="0"
      sandbox="allow-scripts allow-same-origin allow-presentation"
      style={{
        width: '100%',
        overflow: 'auto',
      }}
    />
  )
}

export default HtmlCodeIframeView
