import { styled } from 'styled-components'

export const Paper = styled.div`
  position: relative;
  & .label {
    opacity: 0;
    color: ${({ theme }) => theme.colors.mediumGrey};
    font-size: 12px;
  }
  & .background {
    padding: 8px;
    border-radius: 3px;
  }
  &:hover .background {
    background: ${({ theme }) => theme.colors.highlight};
  }
  &:hover .label {
    opacity: 1;
  }

  & .buttongroup {
    position: absolute;
    right: 0;
    top: 0;
    display: none;
  }

  &:hover .buttongroup {
    display: block;
  }
`

export const Wrapper = styled.div`
  & iframe {
    width: 100%;
    height: ${({ height }) => height || 'calc(100vw / 2.55)'};
    max-width: 600px;
    max-height: 350px;
    margin: 20px auto;
    display: block;
  }
`
