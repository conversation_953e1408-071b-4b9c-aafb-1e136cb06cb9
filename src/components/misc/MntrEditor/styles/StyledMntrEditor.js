import { css, styled } from 'styled-components'
import { Flex } from '~/components/misc/Mntr'

export const StyledEditorWrapper = styled(Flex)`
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 24px;
  z-index: 10;
  width: 100%;
  left: 0;
  right: 0;
  pointer-events: none; /* Make the container non-interactive */
`

export const StyledWrapper = styled.div`
  color: ${({ theme }) => theme.colors.black};
  position: relative;

  & .ProseMirror {
    > * + * {
      margin-top: 0.75em;
    }
  }

  & .ProseMirror p.is-editor-empty:first-child::before {
    color: ${({ theme }) => theme.editor.placeholder};
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }

  ${(props) =>
    props.transparent
      ? css``
      : css`
          border-bottom: 1px solid ${({ theme }) => theme.form.underline};
          background-color: ${({ theme }) => theme.colors.extraLightGrey};
          padding-bottom: 4px;
        `};

  & a {
    color: ${({ theme }) => theme.colors.primary};
  }

  & blockquote {
    border-left: 4px solid ${({ theme }) => theme.colors.disabled};
    margin: 0;
    background-color: ${({ theme }) => theme.colors.background};
    font-style: italic;
    font-weight: 500;
    color: ${({ theme }) => theme.colors.heading};

    & p {
      padding: 8px 16px;
    }
  }

  & span[data-type='merge-tag'] {
    background-image: ${({ theme }) => theme.buttons.keyword};
    padding: 0px 4px;
    border-radius: 4px;
  }
`

export const StyledBubbleMenuWrapper = styled.div`
  border-radius: 19px;
  padding: 3px;
  background: ${({ theme }) => theme.editor.menubar};
  box-shadow:
    0 1px 1px hsl(0deg 0% 0% / 3%),
    0 2px 2px hsl(0deg 0% 0% / 3%),
    0 4px 4px hsl(0deg 0% 0% / 3%),
    0 8px 8px hsl(0deg 0% 0% / 3%),
    0 16px 16px hsl(0deg 0% 0% / 3%);

  opacity: ${({ hidden }) => (hidden ? 0 : 1)};
  transition: opacity 0s ease-in-out;
`

export const StyledFloatingMenuWrapper = styled.div`
  ${({ isFullScreen }) =>
    !isFullScreen &&
    css`
      margin-right: -7px;
    `};
`

export const StyledButtonWrapper = styled(Flex)`
  background: ${({ theme }) => theme.editor.background};
  box-shadow: ${({ theme }) => theme.paper.boxShadow};
  height: 45px;
  align-items: center;
  padding-left: 8px;
  padding-right: 8px;
  border-radius: 20px;
`
