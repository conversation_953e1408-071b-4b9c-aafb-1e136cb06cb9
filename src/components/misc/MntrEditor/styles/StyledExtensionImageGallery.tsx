import { styled } from 'styled-components'

// Extending the default props for the ThumbnailImage component
interface IThumbnailImageProps {
  complete: boolean
}

export const ThumbnailImage = styled.img<IThumbnailImageProps>`
  object-fit: cover;
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  opacity: ${({ complete }) => (complete ? 1 : 0.5)};
  transition: opacity 0.3s;
`

// Extending the default props for the ThumbnailImageWrapper component
interface IThumbnailImageWrapperProps {
  active: boolean
}

export const ThumbnailImageWrapper = styled.div<IThumbnailImageWrapperProps>`
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
  background: ${({ theme, active }) => (active ? theme.colors.primary : 'transparent')};
  padding: 4px;
  height: 80px;
  position: relative;
  width: 130px;
`

// The PreviewImage component doesn't need extra props, so it remains the same
export const PreviewImage = styled.img`
  border-radius: ${({ theme }) => theme.paper.borderRadius}px;
`
