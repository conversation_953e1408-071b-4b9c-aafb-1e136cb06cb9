import { Trans } from '@lingui/react/macro'
import FormEditorCTAButton from '~/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton'

const withModalCTAButton = ({ title, editor, closePopup, color, initialValues }) => {
  return {
    icon: 'edit',
    modalWidth: 780,
    modalTitle: title || <Trans>CTA Button</Trans>,
    modal: (closeModal) => {
      const onSubmit = (model) => {
        editor.chain().focus().setCTAButton(model).run()

        if (typeof closeModal === 'function') {
          closeModal()
        }

        if (typeof closePopup === 'function') {
          closePopup()
        }
      }

      return (
        <FormEditorCTAButton
          onSubmit={onSubmit}
          initialColor={color}
          initialValues={initialValues}
          closeModal={closeModal}
        />
      )
    },
  }
}

export default withModalCTAButton
