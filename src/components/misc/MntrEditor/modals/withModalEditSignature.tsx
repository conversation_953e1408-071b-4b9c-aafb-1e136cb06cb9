import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import identityFn from 'lodash/identity'
import { ReactNode } from 'react'
import { Field } from 'react-final-form'
import MntrTextFieldAdapter from '~/components/forms/adapters/MntrTextFieldAdapter/MntrTextFieldAdapter'
import StyledInputLabel from '~/components/forms/adapters/shared/StyledInputLabel'
import Flex from '~/components/misc/Mntr/Flex'
import Heading from '~/components/misc/Mntr/Heading'
import Text from '~/components/misc/Mntr/Text'
import { PreviewWrapper } from '~/components/misc/MntrEditor/forms/FormEditorCTAButton/FormEditorCTAButton'
import HtmlCodeIframeView from '~/components/misc/MntrEditor/modules/HtmlCodeIframeView'
import MntrForm, { IFormSchemaItem } from '~/components/misc/MntrForm/MntrForm'

interface IModalEditSignatureProps {
  title?: ReactNode
  onSubmit: (model: object) => void
  initialValues?: Record<string, unknown>
}

const withModalEditSignature = ({ title, onSubmit, initialValues }: IModalEditSignatureProps) => {
  return {
    modalWidth: 900,
    modalTitle: title || (
      <span>
        <Trans>Edit signature</Trans>
      </span>
    ),
    modal: (closeModal: () => void) => {
      const schema: IFormSchemaItem[] = [
        {
          customComponent: () => {
            return (
              <Flex flexDirection="column">
                <Heading fontSize={4}>{t`Choose how to add/edit your signature`}</Heading>
                <Text>{t`Manually add the information for your signature, which will appear in every email or customize it using your own HTML.`}</Text>
              </Flex>
            )
          },
        },
        {
          flex: {
            flexDirection: 'row',
            width: [1],
            gap: 3,
          },
          boxes: [
            {
              flex: {
                flexDirection: 'column',
                gap: 3,
                width: [1],
              },
              fields: [
                {
                  customComponent: () => {
                    return <Heading fontSize={3}>{t`Add manually`} </Heading>
                  },
                },
                {
                  name: 'signature_name',
                  label: t`Name`,
                  // @ts-expect-error make default
                  parse: identityFn,
                },
                {
                  name: 'signature_position',
                  label: t`Position`,
                  // @ts-expect-error make default
                  parse: identityFn,
                },
                {
                  name: 'signature_email',
                  label: t`Email`,
                  // @ts-expect-error make default
                  parse: identityFn,
                },
                {
                  name: 'signature_phone',
                  label: t`Phone`,
                  adapter: 'tel',
                  // @ts-expect-error make default
                  parse: identityFn,
                },
              ],
            },
            {
              flex: {
                flexDirection: 'column',
                gap: 3,
                width: [1],
              },
              fields: [
                {
                  customComponent: () => {
                    return <Heading fontSize={3}>{t`Your HTML code`}</Heading>
                  },
                },
                {
                  customComponent: ({ values }) => {
                    return (
                      <Flex flexDirection="column" gap={3}>
                        <Field
                          autoFocus
                          multiline
                          // @ts-expect-error TODO refactor adapters to tsx
                          component={MntrTextFieldAdapter}
                          minRows={5}
                          name="footer"
                          parse={identityFn}
                          label={t`HTML`}
                        />
                        <Flex column>
                          {/* @ts-expect-error: refactor label */}
                          <StyledInputLabel label={t`Preview`} />
                          <PreviewWrapper
                            height={1}
                            overflowY="scroll"
                            // previewMobileDevice={previewMobileDevice}
                          >
                            {!values.footer && (
                              <Flex flex={1} fontSize="14px" fontWeight="bold" center>
                                <Text color="mediumGrey">
                                  {'< '}
                                  {t`Insert HTML code to view preview`}
                                  {' >'}
                                </Text>
                              </Flex>
                            )}

                            {values.footer && (
                              <Flex flex={1} fontSize="14px" fontWeight="bold">
                                <HtmlCodeIframeView value={values.footer} />
                              </Flex>
                            )}
                          </PreviewWrapper>
                        </Flex>
                      </Flex>
                    )
                  },
                },
              ],
            },
          ],
        },
      ]

      return (
        <MntrForm
          contentPadding={3}
          onSubmit={(model) => {
            closeModal()
            onSubmit(model)
          }}
          onCancel={closeModal}
          schema={schema}
          initialValues={initialValues}
        />
      )
    },
  }
}

export default withModalEditSignature
