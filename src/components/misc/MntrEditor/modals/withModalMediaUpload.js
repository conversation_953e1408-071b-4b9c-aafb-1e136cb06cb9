import { t } from '@lingui/core/macro'
import FormMediaUpload from '~/components/misc/MntrEditor/forms/FormMediaUpload/FormMediaUpload'

const withModalMediaUpload = ({
  editor,
  closePopup,
  alt,
  src,
  url,
  isEdit,
  type,
  icon,
  isAttachment,
  handleUploadMedia,
}) => {
  let modalTitle

  switch (type) {
    case 'image':
      modalTitle = t`Upload Image`
      break
    case 'video':
      modalTitle = t`Upload Video`
      break
    default:
      modalTitle = t`Upload File`
  }

  return {
    modalWidth: 900,
    icon: icon || 'edit',
    modalTitle,
    modal: (closeModal) => {
      return (
        <FormMediaUpload
          initialValues={{ image_alt: alt, src: src, image_url: url }}
          closeModal={closeModal}
          closePopup={closePopup}
          editor={editor}
          isEdit={isEdit}
          type={type}
          isAttachment={isAttachment}
          placeholderTitle={modalTitle}
          handleUploadMedia={handleUploadMedia}
        />
      )
    },
  }
}

export default withModalMediaUpload
