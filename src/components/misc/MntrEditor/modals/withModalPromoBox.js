import { Trans } from '@lingui/react/macro'
import FormPromoBoxEditor from '~/components/misc/MntrEditor/forms/FormPromoBoxEditor/FormPromoBoxEditor'

const withModalPromoBox = ({ title, editor, closePopup, color, initialValues }) => {
  return {
    icon: 'edit',
    modalWidth: 780,
    modalTitle: title || <Trans>Promo Box</Trans>,
    modal: (closeModal) => {
      const onSubmit = (model) => {
        editor.chain().focus().setPromoBox(model).run()

        if (typeof closeModal === 'function') {
          closeModal()
        }

        if (typeof closePopup === 'function') {
          closePopup()
        }
      }

      return (
        <FormPromoBoxEditor
          onSubmit={onSubmit}
          initialColor={color}
          initialValues={initialValues}
          closeModal={closeModal}
        />
      )
    },
  }
}

export default withModalPromoBox
