import FormEmbedFacebookUrl from '~/components/misc/MntrEditor/forms/FormEmbedSocialUrl/FormEmbedFacebookUrl'

const withModalEmbedFacebook = ({ title, editor, url, closePopup }) => {
  return {
    icon: 'edit',
    modalWidth: 600,
    modalTitle: title || <span>Facebook</span>,
    modal: (closeModal) => {
      const handleClose = () => {
        closeModal()
        if (typeof closePopup === 'function') {
          closePopup()
        }
      }
      const onSubmit = (model) => {
        editor.chain().focus().setFacebook({ url: model.url }).run()
        handleClose()
      }

      return (
        <FormEmbedFacebookUrl
          placeholder="https://www.facebook.com/{user}/posts/{post_id}/"
          type="facebook"
          initialValues={{ url }}
          onSubmit={onSubmit}
          onCancel={handleClose}
        />
      )
    },
  }
}

export default withModalEmbedFacebook
