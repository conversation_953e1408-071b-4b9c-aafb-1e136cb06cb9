import FormEditorEmbedUrl from '~/components/misc/MntrEditor/modules/FormEditorEmbedUrl'

const withModalEmbedYoutube = ({ title, editor, closePopup, url, isEdit, onUpdate }) => {
  return {
    icon: 'edit',
    modalWidth: 600,
    modalTitle: title || <span>Youtube</span>,
    modal: (closeModal) => {
      const onSubmit = (model) => {
        if (isEdit) {
          onUpdate()
        }

        editor.chain().focus().setYoutube({ url: model.url }).run()

        closeModal()
        if (typeof closePopup === 'function') {
          closePopup()
        }
      }

      return (
        <FormEditorEmbedUrl
          isEdit={isEdit}
          type="youtube"
          placeholderUrl={'https://www.youtube.com/watch?v=dQw4w9WgXcQ'}
          onSubmit={onSubmit}
          initialValues={{ url }}
        />
      )
    },
  }
}

export default withModalEmbedYoutube
