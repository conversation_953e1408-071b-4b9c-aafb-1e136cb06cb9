import { Trans } from '@lingui/react/macro'
import FormEditorHtmlCode from '~/components/misc/MntrEditor/forms/FormEditorHtmlCode/FormEditorHtmlCode'

const withModalHtmlCode = ({
  icon,
  title,
  editor,
  closePopup,
  color,
  initialValues,
  mergeTags = [],
}) => {
  return {
    icon: icon || 'edit',
    fullHeight: true,
    modalWidth: 1920,
    modalTitle: title || <Trans>Custom HTML Code</Trans>,
    modal: (closeModal) => {
      const onSubmit = (model) => {
        editor.chain().focus().setHtmlCode(model).run()

        if (typeof closeModal === 'function') {
          closeModal()
        }

        if (typeof closePopup === 'function') {
          closePopup()
        }
      }

      return (
        <FormEditorHtmlCode
          onSubmit={onSubmit}
          initialColor={color}
          initialValues={initialValues}
          closeModal={closeModal}
          mergeTags={mergeTags}
        />
      )
    },
  }
}

export default withModalHtmlCode
