import FormEditorEmbedUrl from '~/components/misc/MntrEditor/modules/FormEditorEmbedUrl'

const withModalEmbedVimeo = ({ title, editor, closePopup, url, isEdit, onUpdate }) => {
  return {
    icon: 'edit',
    modalWidth: 600,
    modalTitle: title || <span>Vimeo</span>,
    modal: (closeModal) => {
      const onSubmit = (model) => {
        if (isEdit) {
          onUpdate()
        }

        editor.chain().focus().setVimeo({ url: model.url }).run()

        closeModal()
        if (typeof closePopup === 'function') {
          closePopup()
        }
      }

      return (
        <FormEditorEmbedUrl
          isEdit={isEdit}
          type="vimeo"
          placeholderUrl={'https://vimeo.com/719785796'}
          onSubmit={onSubmit}
          initialValues={{ url }}
        />
      )
    },
  }
}

export default withModalEmbedVimeo
