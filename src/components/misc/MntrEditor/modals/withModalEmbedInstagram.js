import FormEditorEmbedUrl from '~/components/misc/MntrEditor/modules/FormEditorEmbedUrl'

const withModalEmbedInstagram = ({ title, editor, closePopup, url, isEdit }) => {
  return {
    icon: 'edit',
    modalWidth: 600,
    modalTitle: title || <span>Instagram</span>,
    modal: (closeModal) => {
      const onSubmit = (model) => {
        editor.chain().focus().setInstagram({ url: model.url }).run()
        if (typeof closeModal === 'function') {
          closeModal()
        }

        if (typeof closePopup === 'function') {
          closePopup()
        }
      }

      return (
        <FormEditorEmbedUrl
          isEdit={isEdit}
          type="instagram"
          onSubmit={onSubmit}
          placeholderUrl={'https://www.instagram.com/p/CcWuEMHrifd'}
          initialValues={{ url }}
        />
      )
    },
  }
}

export default withModalEmbedInstagram
