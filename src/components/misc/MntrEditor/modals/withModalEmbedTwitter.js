import FormEditorEmbedUrl from '~/components/misc/MntrEditor/modules/FormEditorEmbedUrl'

const withModalEmbedTwitter = ({ title, editor, closePopup, url, isEdit }) => {
  return {
    icon: 'edit',
    modalWidth: 600,
    modalTitle: title || <span>X.com</span>,
    modal: (closeModal) => {
      const onSubmit = (model) => {
        editor.chain().focus().setTwitter({ url: model.url }).run()
        closeModal()
        closePopup()
      }

      return (
        <FormEditorEmbedUrl
          isEdit={isEdit}
          type="twitter"
          placeholderUrl={'https://x.com/mediaboard_com/status/1404433924609253381'}
          onSubmit={onSubmit}
          initialValues={{ url }}
        />
      )
    },
  }
}

export default withModalEmbedTwitter
