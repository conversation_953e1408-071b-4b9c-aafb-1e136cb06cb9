import { Editor } from '@tiptap/core'
import FormImageGalleryUpload from '~/components/misc/MntrEditor/forms/FormImageGalleryUpload/FormImageGalleryUpload'

interface IWithModalImageGalleryProps {
  closePopup?: () => void
  editor?: Editor
  gallery?: string[]
  isEdit?: boolean
}

const withModalImageGallery = ({
  editor,
  closePopup,
  isEdit,
  gallery,
}: IWithModalImageGalleryProps) => {
  let modalTitle

  return {
    modalWidth: 900,
    icon: 'edit',
    modalTitle,
    modal: (closeModal: () => void) => {
      return (
        <FormImageGalleryUpload
          initialValues={{}}
          closeModal={closeModal}
          closePopup={closePopup}
          editor={editor}
          gallery={gallery}
          isEdit={isEdit}
        />
      )
    },
  }
}

export default withModalImageGallery
