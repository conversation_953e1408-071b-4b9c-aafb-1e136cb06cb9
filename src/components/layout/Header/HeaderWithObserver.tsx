import { useLingui } from '@lingui/react/macro'
import color from 'color'
import get from 'lodash/get'
import { useRouter } from 'next/router'
import queryString from 'query-string'
import { useEffect } from 'react'

import SearchAdmin from '~/components/forms/dashboard/Search/SearchAdmin'
import SearchForm from '~/components/forms/dashboard/Search/SearchForm'
import MonitoringNavigation from '~/components/layout/Sidebar/modules/MonitoringNavigation/MonitoringNavigation'
import Logo from '~/components/misc/Logo/Logo'
import { Box } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import { getStartPath } from '~/helpers/auth'
import { ObservedFC, observer } from '~/helpers/msts'
import { Link, routerPush } from '~/helpers/router'
import { CustomLogo, LogoWrapper, StyledFormWrapper } from '.'
import AppNotificationsWithObserver from './AppNotifications/AppNotificationsWithObserver'
import Header from './Header'
import SearchSuggest from './SearchSuggest/SearchSuggest'
import UserMenuWithObserver from './UserMenu/UserMenuWithObserver'

const WebViewCallback: ObservedFC = ({ appStore: { theme } }) => {
  // This side effect sends the theme primary color to our mobile app's WebView.
  // It's only called when a theme change occurs and when `StyledHeaderFlex` is
  // visible. This seemed like the simplest way to achieve it without tampering
  // the current header visibility logic.
  useEffect(() => {
    window.ReactNativeWebView?.postMessage(
      JSON.stringify(['MNTR_THEME', { primary_color: color(theme.colors.primary).hex() }]),
    )

    return () => {
      window.ReactNativeWebView?.postMessage(
        JSON.stringify(['MNTR_THEME', { primary_color: 'none' }]),
      )
    }
  }, [theme])

  return null
}

const ThemeCallback = observer(WebViewCallback)

const HeaderWithObserver: ObservedFC = ({
  appStore: { monitoring, searchBar, topics, account, viewport, filter, tvr },
}) => {
  const router = useRouter()
  const { t } = useLingui()

  const {
    isMobileSearchVisible,
    setIsMobileSearchVisible,
    isOpenSearch,
    setIsOpenSearch,
    workspacesData,
  } = searchBar

  // skip rendering if user is not logged in
  if (!account.isLoaded) {
    return null
  }

  const customLogo = account.user.logo_image_url
  const pathname = router.pathname
  const isAdmin = pathname.startsWith('/staff/admin')
  const isMedialist = pathname.startsWith('/authors')
  const isChangelog = pathname.endsWith('/changelog')

  const onSearch = (query: { query: string }) => {
    setIsOpenSearch(false)
    setIsMobileSearchVisible(false)

    filter.addFilterItem({
      type: 'query',
      value: query.query,
      id: query.query,
    })

    const queryToRestore = queryString.parse(monitoring.inspector.queryToRestore)
    const suggestedWorkspaces = workspacesData

    if (isChangelog) {
      return routerPush(
        `${window.location.pathname}?${filter.urlWithParam({ query: query.query })}`,
      )
    }

    if (isAdmin) {
      if (suggestedWorkspaces.length === 1) {
        const workspace = suggestedWorkspaces[0]
        const exactId = Number(workspace.id) === Number(query.query)
        const exactName = workspace.name.toLowerCase() === query.query.trim().toLowerCase()

        if (exactId || exactName) return routerPush(`/staff/admin/workspaces/${workspace.id}`)
      }

      return routerPush(`/staff/admin/customers?${queryString.stringify(query)}`)
    }

    switch (pathname) {
      case '/article':
        monitoring.inspector.resetLastOpenFeedItem()
        queryToRestore.query = query.query
        return routerPush(`/?${queryString.stringify(Object.assign({}, queryToRestore))}`)

      case '/crisis-communication':
        tvr.inspector.close()
        return routerPush(`/crisis-communication?${filter.urlWithParam({ query: query.query })}`)

      case '/analytcs':
        return routerPush(`/analytics?${filter.urlWithParam({ query: query.query })}`)

      case '/author/[authorId]':
        return routerPush(
          `/author/${router.query.authorId}?${filter.urlWithParam({ query: query.query })}`,
        )

      case '/authors':
        return routerPush(`/authors?${filter.urlWithParam({ query: query.query })}`)

      default:
        if (
          account.workspace?.permissions.archive_feed.can_read ||
          account.workspace?.permissions.monitoring_feed.can_read
        ) {
          routerPush(`/?${filter.urlWithParam({ query: query.query })}`)
        }

        return
    }
  }

  const isQueryFocused = filter.isQueryFocused || get(filter, 'data.query')
  const query = get(filter, 'data.query')

  if (account.workspace?.is_expired && !account.user.isImpersonating) {
    return false
  }

  return (
    <Header
      isImpersonating={account.user.isImpersonating}
      isLoaded={account.isLoaded}
      isMobileSearchVisible={searchBar.isMobileSearchVisible}
      pathname={router.pathname}
      setIsMobileSearchVisible={searchBar.setIsMobileSearchVisible}
      user={account.user}
      workspace={account.workspace}
      monitoringNavigationComponent={(close) => (
        <MonitoringNavigation mobileNavigation onClick={close} />
      )}
      notificationsComponent={<AppNotificationsWithObserver />}
      logoComponent={
        /* @ts-expect-error TODO refactor Link to tsx */
        <Link href={getStartPath(account.workspace)}>
          {customLogo ? (
            <CustomLogo url={customLogo} />
          ) : (
            <LogoWrapper>
              <Logo mono />
            </LogoWrapper>
          )}
        </Link>
      }
      searchAdminComponent={<SearchAdmin />}
      searchInputComponent={
        <>
          {((viewport.width > 0 && !viewport.isMobile && !account.user.is_public_access) ||
            isMobileSearchVisible) && (
            <StyledFormWrapper
              istablet={viewport.isTablet}
              ismobile={viewport.isMobile}
              isqueryfocused={isQueryFocused}
            >
              <SearchForm
                initialValues={{
                  query: pathname !== '/export' ? query || '' : '',
                }}
                filter={filter}
                pathname={pathname}
                onSubmit={onSearch}
                topics={topics}
                isQueryFocused={isQueryFocused}
                canReadArchive={account.workspace?.permissions.archive_feed.can_read}
                isOpenSearch={isOpenSearch}
              />
            </StyledFormWrapper>
          )}
          {filter.data &&
            viewport.width > 0 &&
            !viewport.isMobile &&
            !account.user.is_public_access &&
            !isAdmin &&
            !isMedialist && (
              <MntrButton
                ml={1}
                mr={1}
                bg="header"
                icon="help"
                tooltip={t`Search help`}
                href="/help/search"
                target="_blank"
              />
            )}
        </>
      }
      searchSuggestComponent={<SearchSuggest />}
      themeCallbackComponent={<ThemeCallback />}
      userMenuComponent={(closePopup) => <UserMenuWithObserver closePopup={closePopup} />}
      searchHandlerComponent={
        <>
          {viewport.isMobile && !isMobileSearchVisible && (
            <MntrButton
              mr={2}
              icon="search"
              bg="header"
              onClick={() => {
                setIsMobileSearchVisible(true)

                setTimeout(() => {
                  const input = document.querySelector('.search-input input') as HTMLInputElement

                  if (input) {
                    input.focus()
                  }
                }, 50)
              }}
            />
          )}

          {viewport.isMobile &&
            isMobileSearchVisible &&
            filter.data &&
            viewport.width > 0 &&
            !account.user.is_public_access &&
            !isAdmin &&
            !isMedialist && (
              <Box mr={-2} ml={1}>
                <MntrButton bg="header" icon="help" tooltip={t`Search help`} href="/help/search" />
              </Box>
            )}
        </>
      }
    />
  )
}

export default observer(HeaderWithObserver)
