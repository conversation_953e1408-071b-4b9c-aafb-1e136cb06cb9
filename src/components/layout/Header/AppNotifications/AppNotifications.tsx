import { Trans } from '@lingui/react/macro'
import { useEffect } from 'react'
import { styled } from 'styled-components'
import { Box, Text } from '~/components/misc/Mntr'
import MntrButton from '~/components/misc/MntrButton/MntrButton'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import appNotificationActions from '~/components/notifications/AppNotifications/NotificationsList/appNotificationActions'
import dateFromNow from '~/helpers/date/dateFromNow'

const PushNotificationsWrapper = styled(Box)`
  background: ${({ theme }) => theme.table.background};
  text-align: center;
`

const NOTIFICATIONS_LIMIT = 5

interface IAppNotificationsProps {
  isMobileApp: boolean
  account: unknown
  appNotifications: unknown
  redirectTo: string
}

const AppNotifications = ({
  isMobileApp,
  account,
  appNotifications,
  redirectTo,
}: IAppNotificationsProps) => {
  useEffect(() => {
    const removeWebSocketListener = appNotifications.addWebSocketListener()
    return () => {
      removeWebSocketListener()
    }
  }, [appNotifications])

  return (
    <MntrButton
      data-length={appNotifications.dataPopup.length}
      data-visibleSettings={appNotifications.isVisibleSettings ? 'true' : 'false'}
      data-subscription={appNotifications.subscription.disabled_items.length}
      bg="header"
      mr={1}
      badge={appNotifications.isOpenPopup ? 0 : appNotifications.unreadCount}
      icon={appNotifications.unreadCount ? 'notifications_active' : 'notifications'}
      popupPlacement={'bottom-end'}
      transformOrigin="100% 0"
      popupWidth={300}
      zIndex={5000}
      onOpen={() => {
        appNotifications.markOpenPopup(true)
      }}
      onClose={() => {
        appNotifications.markOpenPopup(false)
        if (appNotifications.isVisibleSettings) {
          appNotifications.markVisibleSettings(false)
        }
        appNotifications.unreadAll()
      }}
      popup={(closePopup) => {
        const createMenuItem = (item) => {
          return {
            label: item.title,
            secondaryText: dateFromNow(item.created),
            leftIcon: item.icon,
            leftIconColor: 'secondary',
            href: item.url,
            onClick(event) {
              // TODO: Fix/remove in
              // https://github.com/monitora-media/monitora-frontend/pull/1214
              if (item.url.startsWith('/')) {
                event.preventDefault()
                window.location.href = item.url
              }
            },
            keepButtonGroupVisible: true,
            buttonGroup: [
              {
                icon: 'more_vert',
                mr: 1,
                size: 'small',
                zIndex: 9999,
                popupPlacement: 'bottom-end',
                transformOrigin: '100% 0',
                popup: (closePopup) => {
                  const notificationActions = appNotificationActions(
                    item,
                    closePopup,
                    appNotifications,
                  )
                  return <MntrMenu menuItems={notificationActions} closePopup={closePopup} />
                },
              },
            ],
          }
        }

        const settingsButtonGroup = [
          {
            icon: 'settings',
            mr: 1,
            size: 'small',
            tooltip: <Trans>Notification Settings</Trans>,
            onClick: () => appNotifications.markVisibleSettings(true),
          },
        ]

        const menuItems = []
        const menuItemsSettings = [
          {
            label: <Trans>Notification Settings</Trans>,
          },
          {
            label: <Trans>Back</Trans>,
            leftIcon: 'chevron_left',
            onClick: () => appNotifications.markVisibleSettings(false),
          },
        ]

        account.enums.app_notifications.app_notification_type.forEach((item) => {
          const isDisabled = appNotifications.subscription.isDisableById(item.id)
          menuItemsSettings.push({
            label: item.text,
            leftIcon: isDisabled ? 'toggle_off' : 'toggle_on',
            leftIconColor: isDisabled ? 'error' : 'secondary',
            hoverVariant: isDisabled ? 'error' : 'secondary',
            onClick: () => {
              appNotifications.subscription.toggleSubscription(item.id)
            },
          })
        })

        const newNotificationsArr = appNotifications.dataPopup.filter((item) => !item.is_read)

        if (newNotificationsArr.length) {
          menuItems.push({
            label: <Trans id="new.notifications">New</Trans>,
            buttonGroup: settingsButtonGroup,
          })

          newNotificationsArr.forEach((item, index) => {
            if (index >= NOTIFICATIONS_LIMIT) {
              return false
            }
            menuItems.push(createMenuItem(item))
          })
        }

        const notificationsPopupdata = appNotifications.dataPopup.filter((item) => item.is_read)

        if (
          newNotificationsArr.length < NOTIFICATIONS_LIMIT &&
          (notificationsPopupdata.length || appNotifications.dataPopup.length === 0)
        ) {
          menuItems.push({
            label: <Trans>Notifications</Trans>,
            buttonGroup: newNotificationsArr.length ? [] : settingsButtonGroup,
          })
        }

        notificationsPopupdata.forEach((item, index) => {
          if (index >= NOTIFICATIONS_LIMIT - newNotificationsArr.length) {
            return false
          }
          menuItems.push(createMenuItem(item))
        })

        return (
          <Box maxWidth={['auto', 'auto', '400px']}>
            {appNotifications.isVisibleSettings && <MntrMenu menuItems={menuItemsSettings} />}
            {!appNotifications.isVisibleSettings && (
              <>
                <MntrMenu menuItems={menuItems} closePopup={closePopup} />
                {appNotifications.isVisibleEmptyMessagePopup && (
                  <Box>
                    <Text pb={3} mx={3} color="mediumGrey">
                      <Trans>You have not received any notifications yet.</Trans>
                    </Text>
                  </Box>
                )}
                {!appNotifications.isVisibleEmptyMessagePopup && (
                  <Box textAlign="center" pb={2}>
                    <MntrButton
                      rounded
                      icon="expand_more"
                      onClick={() => {
                        redirectTo('/app-notifications')
                        closePopup()
                      }}
                      label={<Trans>Show All</Trans>}
                      href={`/app-notifications`}
                    />
                  </Box>
                )}
                {isMobileApp && (
                  <PushNotificationsWrapper py={2}>
                    <MntrButton
                      rounded
                      bg="tertiary"
                      icon="app_settings_alt"
                      onClick={() => {
                        redirectTo('/push-notifications')
                        closePopup()
                      }}
                      label={<Trans>Push Notifications</Trans>}
                      href={`/push-notifications`}
                    />
                  </PushNotificationsWrapper>
                )}
              </>
            )}
          </Box>
        )
      }}
    />
  )
}

export default AppNotifications
