import { useEffect } from 'react'
import { ObservedFC, observer } from '~/helpers/msts'
import AppNotifications from './AppNotifications'

const AppNotificationsWithObserver: ObservedFC = ({
  appStore: {
    isMobileApp,
    account,
    appNotifications,
    router: { redirectTo },
  },
}) => {
  useEffect(() => {
    const removeWebSocketListener = appNotifications.addWebSocketListener()
    return () => {
      removeWebSocketListener()
    }
  }, [appNotifications])

  return (
    <AppNotifications
      isMobileApp={isMobileApp}
      account={account}
      appNotifications={appNotifications}
      redirectTo={redirectTo}
    />
  )
}

export default observer(AppNotificationsWithObserver)
