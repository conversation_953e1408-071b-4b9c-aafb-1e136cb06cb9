'use client'

import { useEffect } from 'react'
import { useGTM } from '~/app/lib/use-gtm'
import events from '~/constants/gtm'

export default function GTMTests() {
  const { pushEvent, pushEventAuto } = useGTM()

  useEffect(() => {
    pushEventAuto(events.ANALYTICS_LOADED)
  })

  return (
    <div style={{ display: 'flex', gap: '10px' }}>
      <button onClick={() => pushEvent(events.ANALYTICS_LOADED)}>Push event (no params)</button>
      <button onClick={() => pushEvent(events.ANALYTICS_LOADED, { a: 1 })}>
        Push event (1 param)
      </button>
      <button onClick={() => pushEvent(events.ANALYTICS_LOADED, { a: 2, b: 1 })}>
        Push event (2 params)
      </button>
    </div>
  )
}
