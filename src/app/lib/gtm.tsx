import { GoogleTagManager } from '@next/third-parties/google'
import { headers } from 'next/headers'
import type { UserProps } from '~/app/types/user'
import type { IWorkspaceStore } from '~/store/models/account/workspace/WorkspaceStore'
import type { IAppSettings } from '~/store/models/appSettings/AppSettings'
import { getDeviceInfo } from './get-device-info'
import { getSessionAndSettings } from './get-session-settings'

export async function GTM({ containerId }: { containerId: string }) {
  const { appSettings, requestIp, sessionInfo } = await getSessionAndSettings()
  const { isMobileApp } = await getDeviceInfo()
  const headersList = await headers()
  const host = headersList.get('host') ?? ''

  return (
    <>
      <GoogleTagManager
        gtmId={containerId}
        dataLayer={{
          buildEnv: process.env.NODE_ENV,
          ipAddress: requestIp,
          isMobileApp,
          productDomain: host.replace('app.', ''),
          productId: appSettings.gtmProductId,
          primaryApp: sessionInfo.primaryApp,
        }}
      />
      <noscript>
        <iframe
          src={`https://www.googletagmanager.com/ns.html?id=${containerId}`}
          height={0}
          width={0}
          style={{ display: 'none', visibility: 'hidden' }}
        />
      </noscript>
    </>
  )
}

export function createUserData(
  user: UserProps,
  workspace: IWorkspaceStore,
  appSettings: IAppSettings,
  userLanguage: string,
  isImpersonating: boolean,
) {
  return {
    email: user.email,
    isAnalyst: user.is_analyst,
    isSalesman: user.is_salesman,
    isImpersonating: isImpersonating,
    isPublicAccess: false,
    userId: user.uuid,
    userLanguage,
    workspaceId: workspace.uuid,
    primaryApp: appSettings.primaryApp,
    productId: appSettings.gtmProductId,
  }
}
