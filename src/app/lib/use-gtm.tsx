'use client'

import { sendGTMEvent } from '@next/third-parties/google'
import { useContext } from 'react'
import { IsAuthContext } from '~/app/lib/is-auth-context'
import { eventNames } from '~/constants/gtm'

let gtmData: Record<string, unknown> = {}
let waitForAuth = false

async function isGTMReady(): Promise<boolean> {
  return new Promise((resolve) => {
    const wait = setInterval(() => {
      if (
        // Wait for PostHog to be loaded
        window.posthog?.__loaded &&
        // Wait for user data
        (!waitForAuth || !!gtmData.userId)
      ) {
        clearInterval(wait)
        resolve(true)
      }
    }, 100)
  })
}

export function setGTMData(data: Record<string, unknown>) {
  gtmData = data
}

function isValid(eventName: string): boolean {
  return Boolean(eventName && typeof window !== 'undefined')
}

type ActionParams = Record<string, unknown> | Record<string, unknown>[] | null

function formatEvent(eventName: string): string {
  return eventNames.includes(eventName) ? `custom:${eventName}` : eventName
}

function formatParams(actionParams?: ActionParams) {
  // TODO: Refactor (also on GTM side) to use a simpler structure.
  return actionParams
    ? { actionParams: Array.isArray(actionParams) ? actionParams : [actionParams] }
    : { actionParams: null }
}

async function executeEvent(
  eventName: string,
  actionParams?: ActionParams,
  isInteractive: boolean = true,
) {
  if (!isValid(eventName)) return

  await isGTMReady()

  sendGTMEvent({
    ...gtmData,
    event: formatEvent(eventName),
    ...formatParams(actionParams),
    isInteractive,
    _clear: true,
  })
}

export function pushEvent(eventName: string, actionParams?: ActionParams) {
  executeEvent(eventName, actionParams, true)
}

export function pushEventAuto(eventName: string, actionParams?: ActionParams) {
  executeEvent(eventName, actionParams, false)
}

export function useGTM() {
  const isAuthContext = useContext(IsAuthContext)

  waitForAuth = isAuthContext

  return {
    pushEvent,
    pushEventAuto,
  }
}
