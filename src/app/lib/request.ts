import 'server-only'

import { headers } from 'next/headers'
import { userAgent } from 'next/server'
import { getClientIp } from 'request-ip'

import { getAuthTokens } from '~/app/actions/app-cookies'
import logger from './logger'

interface RequestOptions extends RequestInit {
  headers?: Record<string, string>
}

async function request(endpoint: string, options?: RequestOptions): Promise<Request> {
  const headersList = await headers()
  const { ua } = userAgent({ headers: headersList })
  const { token } = await getAuthTokens()

  const url = `${process.env.API_URL}${endpoint}`
  logger.info({ url }, 'App Router request')
  return new Request(url, {
    ...options,
    headers: {
      ...options?.headers,
      'x-mb-cookie': headersList.get('cookie') ?? '',
      'x-mb-ip-address': getClientIp({ headers: Object.fromEntries(headersList.entries()) }) ?? '',
      'x-mb-user-agent': ua,
      Authorization: `Token ${token}`,
    },
  })
}

export async function get(endpoint: string, options?: RequestOptions): Promise<Request> {
  return request(endpoint, {
    ...options,
  })
}

export async function post(endpoint: string, body: Record<string, unknown> = {}): Promise<Request> {
  return request(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  })
}

export async function patch(
  endpoint: string,
  body: Record<string, unknown> = {},
): Promise<Request> {
  return request(endpoint, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  })
}
