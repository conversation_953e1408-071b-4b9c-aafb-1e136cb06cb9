'use client'

import { sendGTMEvent } from '@next/third-parties/google'
import { differenceInDays } from 'date-fns'
import cookie from 'js-cookie'
import { useEffect } from 'react'
import { createUserData } from './gtm'
import { setGTMData } from './use-gtm'

export function GTMUserIdent({ userData }: { userData: ReturnType<typeof createUserData> }) {
  useEffect(() => {
    setGTMData(userData)
    addUserIdent(userData)

    return () => {
      // TODO: Split gtmData into two objects: one for the base data and one for
      // the user data. We should only nullify the user data properties.
      setGTMData(Object.fromEntries(Object.entries(userData).map(([key]) => [key, null])))
    }
  }, [userData])

  return null
}

function addUserIdent(userData: ReturnType<typeof createUserData>) {
  sendGTMEvent({
    ...userData,
    event: 'login',
  })

  if (userData.isSalesman) {
    // Force Leady opt-out for salesmen
    const MIN = new Date()
    const MAX = new Date(2038, 0, 19, 3, 14, 7) // Epochalypse 😱

    cookie.set('leady_opt_out', JSON.stringify([true, MIN, MAX]), {
      domain: location.host.replace('app.', ''),
      expires: differenceInDays(MAX, MIN),
    })
  }

  if (!userData.isImpersonating) {
    // Leady user identification
    // https://leady.com/websites/.mediaboard.com/connect/#events
    window._leady = window._leady || []
    window._leady.push(['identify', userData.email])
  }
}
