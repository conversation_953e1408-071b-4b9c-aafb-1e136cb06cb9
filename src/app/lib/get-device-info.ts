import { headers } from 'next/headers'
import { userAgent } from 'next/server'

export interface DeviceInfo {
  browserName: string | undefined
  browserVersion: string | undefined
  deviceType: string | undefined
  isBot: boolean
  isMobileApp: boolean
  osName: string | undefined
  osVersion: string | undefined
  userAgent: string
}

export async function getDeviceInfo(): Promise<DeviceInfo> {
  const headersList = await headers()
  const ua = userAgent({ headers: headersList })

  return {
    browserName: ua.browser.name,
    browserVersion: ua.browser.version,
    deviceType: ua.device.type,
    isBot: ua.isBot,
    isMobileApp: ua.ua.includes('MbExpoWebView'),
    osName: ua.os.name,
    osVersion: ua.os.version,
    userAgent: ua.ua,
  }
}
