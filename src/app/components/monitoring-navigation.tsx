'use client'

import { useLingui } from '@lingui/react/macro'
import { useActivePath } from '~/app/lib/use-active-path'
import MntrMenu from '~/components/misc/MntrMenu/MntrMenu'
import withWorkspaceArticleModal from '~/components/monitoring/WorkspaceArticles/withWorkspaceArticleModal'
import staticFeeds from '~/constants/staticFeeds'
import withModalRemove from '~/helpers/modal/withModalRemove'
import { IPermissionsStore } from '~/store/models/account/workspace/permissions/PermissionsStore'
import { IWorkspaceStore } from '~/store/models/account/workspace/WorkspaceStore'
import { IAppSettings } from '~/store/models/appSettings/AppSettings'

interface IMonitoringNavigationProps {
  appSettings: IAppSettings
  exportBasketId?: string
  exportList?: unknown
  feedMap?: unknown
  filterUrl?: string
  isImpersonating: boolean
  isSalesman: boolean
  mobileNavigation?: boolean
  onClick?: () => void
  permissions: IPermissionsStore
  workspace: IWorkspaceStore
}

const MonitoringNavigation = ({
  appSettings,
  exportBasketId,
  exportList,
  feedMap,
  filterUrl,
  isImpersonating,
  isSalesman,
  mobileNavigation,
  onClick,
  permissions,
  workspace,
}: IMonitoringNavigationProps) => {
  const { t } = useLingui()
  const activePath = useActivePath()
  const navigation = appSettings?.navigation
  const showSidebarPromoApps = navigation?.showSidebarPromoApps

  const menuItems = []
  const sharedProps = {
    accent: true,
    rounded: !mobileNavigation,
    onClick,
  }

  // @ts-expect-error TODO refactor FeedMapItem to TS
  const exportData = feedMap?.get(staticFeeds.EXPORT_FEED)?.exportStore || exportList

  const { counter, submitClearExportBasket } = exportData || {}

  const visibleTvr = permissions.tvr_feed?.can_read || showSidebarPromoApps.tvr
  const visibleMedialist = permissions.authors_database?.can_read || showSidebarPromoApps.medialist
  const visibleNewsroom = permissions.newsroom_blog?.can_read || showSidebarPromoApps.newsroom
  const visibleEmailing = permissions.newsroom_emailing?.can_read || showSidebarPromoApps.emailing

  if (mobileNavigation) {
    menuItems.push({
      label: t`Monitoring`,
    })
  }

  // Articles
  menuItems.push({
    label: t`Articles`,
    leftIcon: 'view_stream',
    selected: activePath === 'articles',
    href: filterUrl && activePath === 'analytics' ? `/?${filterUrl}` : '/',
    ...sharedProps,
  })

  // Analytics
  if (permissions.analytics?.can_read) {
    menuItems.push({
      label: t`Analytics`,
      leftIcon: 'show_chart',
      selected: activePath === 'analytics',
      href: filterUrl && activePath === 'articles' ? `/analytics?${filterUrl}` : '/analytics',
      ...sharedProps,
    })
  }

  if (workspace?.limits.dashboards_limit > 0) {
    menuItems.push({
      label: t`Dashboard`,
      leftIcon: 'dashboard',
      selected: activePath === 'dashboard',
      href: '/dashboard',
      ...sharedProps,
    })
  }

  // Export
  if (permissions.export?.can_read) {
    menuItems.push({
      label: t`Export`,
      leftIcon: 'inbox',
      selected: activePath === 'export',
      counter: counter,
      href: '/export',
      buttonGroup: [
        {
          icon: 'more_vert',
          size: 'small',
          popupPlacement: 'bottom-start',
          transformOrigin: '100% 0',
          popup: (closePopup: () => void, onClose: () => void) => {
            const menuItems = [
              {
                leftIcon: 'download',
                label: t`Exports to download`,
                href: '/export/history',
              },
              {
                leftIcon: 'delete',
                hoverVariant: 'error',
                label: t`Empty export`,
                disabled: counter === 0,
                ...withModalRemove({
                  onSubmit: () => {
                    submitClearExportBasket(activePath === 'export', exportBasketId)
                  },
                  title: t`Empty export?`,
                  message: t`All articles will be removed from export.`,
                }),
              },
            ]
            return (
              // @ts-expect-error TODO refactor MntrMenu to TS
              <MntrMenu
                menuItems={menuItems}
                closePopup={() => {
                  closePopup()
                  onClose()
                }}
              />
            )
          },
        },
      ],
      ...sharedProps,
    })
  }

  // Topics
  if (permissions.topics?.can_read) {
    menuItems.push({
      label: t`Topics`,
      leftIcon: 'bookmark_border',
      selected: activePath === 'topics',
      href: '/topics',
      ...sharedProps,
    })
  }

  // Reports
  if (permissions.email_reports?.can_read) {
    menuItems.push({
      label: t`Reports`,
      leftIcon: 'mail',
      selected: activePath === 'reports',
      href: '/reports',
      buttonGroup: [
        {
          icon: 'more_vert',
          size: 'small',
          popupPlacement: 'bottom-start',
          transformOrigin: '100% 0',
          popup: (closePopup: () => void, onClose: () => void) => {
            const menuItems = [
              {
                leftIcon: 'forward_to_inbox',
                label: t`Reports History`,
                href: '/reports/history',
              },
            ]
            return (
              // @ts-expect-error TODO refactor MntrMenu to TS
              <MntrMenu
                menuItems={menuItems}
                closePopup={() => {
                  closePopup()
                  onClose()
                }}
              />
            )
          },
        },
      ],
      ...sharedProps,
    })
  }

  // Workspace Articles
  if (workspace?.limits.allow_workspace_articles) {
    menuItems.push({
      href: '/workspace-articles',
      label: t`My Articles`,
      leftIcon: 'post_add',
      selected: activePath === 'workspace-articles',
      buttonGroup: [
        {
          icon: 'more_vert',
          popup: (closePopup: () => void, onClose: () => void) => {
            const menuItems = [
              {
                label: t`Create article`,
                leftIcon: 'post_add',
                ...withWorkspaceArticleModal(),
              },
            ]

            return (
              // @ts-expect-error TODO refactor MntrMenu to TS
              <MntrMenu
                menuItems={menuItems}
                closePopup={() => {
                  closePopup()
                  onClose()
                }}
              />
            )
          },
          popupPlacement: 'bottom-start',
          size: 'small',
          transformOrigin: '100% 0',
        },
      ],
      ...sharedProps,
    })
  }

  // Trash
  if (permissions.monitoring_feed?.can_write) {
    menuItems.push({
      label: t`Trash`,
      leftIcon: 'delete',
      selected: activePath === 'trash',
      href: '/trash',
      ...sharedProps,
    })
  }

  if (mobileNavigation && (visibleTvr || visibleMedialist || visibleNewsroom || visibleEmailing)) {
    menuItems.push({
      label: t`Other`,
    })

    // TVR - burger mobile nav
    if (visibleTvr) {
      menuItems.push({
        label: t`Crisis communication`,
        leftIcon: 'campaign',
        selected: activePath === 'tvr',
        href: '/crisis-communication',
        ...sharedProps,
      })
    }

    // Medialist
    if (visibleMedialist) {
      menuItems.push({
        label: t`Medialist`,
        leftIcon: 'groups',
        selected: activePath === 'authors',
        href: '/authors',
        ...sharedProps,
      })
    }

    if (visibleNewsroom) {
      menuItems.push({
        label: t`Newsroom`,
        leftIcon: 'newspaper',
        selected: activePath === 'newsroom',
        href: '/newsroom',
        ...sharedProps,
      })
    }

    // Emailing
    if (visibleEmailing) {
      menuItems.push({
        label: t`Emailing`,
        leftIcon: 'outgoing_mail',
        selected: activePath === 'emailing',
        href: '/emailing',
        ...sharedProps,
      })
    }
  }

  // Admin
  if (mobileNavigation && (isSalesman || isImpersonating)) {
    menuItems.push(
      {
        label: t`Admin`,
      },
      {
        label: t`Customers`,
        selected: activePath === 'admin',
        leftIcon: 'contact_page',
        href: '/staff/admin/customers',
        ...sharedProps,
      },
    )
  }
  // @ts-expect-error TODO refactor MntrMenu to TS
  return <MntrMenu menuItems={menuItems} />
}

export default MonitoringNavigation
