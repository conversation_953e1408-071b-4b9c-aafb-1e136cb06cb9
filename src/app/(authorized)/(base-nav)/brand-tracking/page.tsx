import { msg } from '@lingui/core/macro'
import type { Metadata } from 'next'
import { notFound } from 'next/navigation'

import { getLang } from '~/app/actions/i18n'
import { getInit } from '~/app/actions/init'
import { FullSizeIframe } from '~/app/components/full-size-iframe'
import { getI18nInstance } from '~/app/lib/app-router-i18n'

export async function generateMetadata(): Promise<Metadata> {
  const lang = await getLang()
  const i18n = getI18nInstance(lang)

  return {
    title: i18n._(msg`Brand Tracking`),
  }
}

export default async function BrandTrackingPage() {
  const { workspace } = await getInit()

  if (!workspace?.limits.allow_behavio) {
    notFound()
  }

  return <FullSizeIframe src="https://grow.behavio.app/bt-embed/keyMetrics/overview" />
}
