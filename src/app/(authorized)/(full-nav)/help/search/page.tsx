import { msg } from '@lingui/core/macro'
import type { Metadata } from 'next'

import { getLang } from '~/app/actions/i18n'
import { Content, Sidebar } from '~/app/components/sidebar-layout'
import { getI18nInstance } from '~/app/lib/app-router-i18n'
import { getSessionAndSettings } from '~/app/lib/get-session-settings'
import HelpNavigation from '~/components/layout/Sidebar/modules/HelpNavigation/HelpNavigation'
import HelpContent from './content'

export async function generateMetadata(): Promise<Metadata> {
  const lang = await getLang()
  const i18n = getI18nInstance(lang)

  return {
    title: i18n._(msg`Search help`),
  }
}

export default async function HelpPage() {
  const { appSettings } = await getSessionAndSettings()
  const { appName } = appSettings

  return (
    <>
      <Sidebar>
        <HelpNavigation />
      </Sidebar>
      <Content>
        <HelpContent appName={appName} />
      </Content>
    </>
  )
}
