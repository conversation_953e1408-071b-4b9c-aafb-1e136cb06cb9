version: '3.8'

services:
  mediaboard-ws:
    build: .
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - WS_PORT=8081
      - WS_HOST=0.0.0.0
      - CORS_ORIGIN=http://localhost:3000
      - JWT_SECRET=${JWT_SECRET:-change-this-in-production}
      - JWT_EXPIRES_IN=24h
      - LOG_LEVEL=info
      - LOG_PRETTY=false
      - RATE_LIMIT_WINDOW_MS=60000
      - RATE_LIMIT_MAX_REQUESTS=100
      - HEALTH_CHECK_INTERVAL=30000
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:8080/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add Redis for scaling (future enhancement)
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped

# volumes:
#   redis_data:
