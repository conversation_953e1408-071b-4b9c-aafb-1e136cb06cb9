// WebSocket Notification Testing Script
// Run this in the browser's developer console to test notifications

// Test function to send a notification via the WebSocket server
async function testNotification() {
  try {
    const response = await fetch('http://localhost:8080/notify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'AppNotificationsLog',
        method: 'CREATE',
        data: [
          {
            id: Date.now(), // Use timestamp as unique ID
            app_notification_type: 1, // APP_NEWS type
            is_read: false,
            icon: 'notifications',
            title: 'Test WebSocket Notification',
            url: '/app-notifications',
            created: new Date().toISOString(),
          }
        ]
      })
    });

    const result = await response.json();
    console.log('Notification sent:', result);
    
    if (result.success) {
      console.log('✅ Notification should appear in the header notifications bell');
      console.log('Check the notifications icon in the top-right corner of the app');
    }
  } catch (error) {
    console.error('❌ Failed to send notification:', error);
  }
}

// Test function to send a workspace articles notification
async function testWorkspaceArticlesNotification() {
  try {
    const response = await fetch('http://localhost:8080/notify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'AppNotificationsLog',
        method: 'CREATE',
        data: [
          {
            id: Date.now() + 1,
            app_notification_type: 3, // WORKSPACE_ARTICLES type
            is_read: false,
            icon: 'article',
            title: 'New Workspace Article',
            url: '/workspace-articles',
            created: new Date().toISOString(),
          }
        ]
      })
    });

    const result = await response.json();
    console.log('Workspace articles notification sent:', result);
  } catch (error) {
    console.error('❌ Failed to send workspace articles notification:', error);
  }
}

// Test function to check WebSocket connection status
function checkWebSocketConnection() {
  // Access the app store from the global window object
  if (window.__NEXT_DATA__ && window.appStore) {
    const websocketUrl = window.appStore.account.websocket_url;
    const lastMessage = window.appStore.websocketMessages.lastMessage;
    
    console.log('WebSocket URL:', websocketUrl);
    console.log('Last WebSocket message:', lastMessage);
    console.log('Notifications count:', window.appStore.appNotifications.unreadCount);
    
    if (websocketUrl) {
      console.log('✅ WebSocket URL is configured');
    } else {
      console.log('❌ WebSocket URL is not configured');
      console.log('Make sure the backend /init/ endpoint returns websocket_url');
    }
  } else {
    console.log('❌ App store not accessible. Make sure you\'re on the Monitora app page.');
  }
}

// Instructions
console.log(`
🧪 WebSocket Notification Testing

1. First, check WebSocket connection:
   checkWebSocketConnection()

2. Send a test notification:
   testNotification()

3. Send a workspace articles notification:
   testWorkspaceArticlesNotification()

4. Check the notifications bell icon in the header - it should show a badge with the number of unread notifications

5. Click the notifications bell to see the notification details

Note: Make sure the WebSocket server is running on localhost:8080
`);

// Export functions to global scope for easy access
window.testNotification = testNotification;
window.testWorkspaceArticlesNotification = testWorkspaceArticlesNotification;
window.checkWebSocketConnection = checkWebSocketConnection;
