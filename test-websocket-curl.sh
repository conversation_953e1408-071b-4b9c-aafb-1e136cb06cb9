#!/bin/bash

# WebSocket Notification Testing with curl
# Make sure the WebSocket server is running on localhost:8080

echo "🧪 Testing WebSocket Notifications with curl"
echo ""

# Test 1: Send a basic app notification
echo "📱 Sending basic app notification..."
curl -X POST http://localhost:8080/notify \
  -H "Content-Type: application/json" \
  -d '{
    "model": "AppNotificationsLog",
    "method": "CREATE",
    "data": [
      {
        "id": 1001,
        "app_notification_type": 1,
        "is_read": false,
        "icon": "notifications",
        "title": "Test Notification from curl",
        "url": "/app-notifications",
        "created": "'$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")'"
      }
    ]
  }'

echo ""
echo ""

# Test 2: Send a workspace articles notification
echo "📄 Sending workspace articles notification..."
curl -X POST http://localhost:8080/notify \
  -H "Content-Type: application/json" \
  -d '{
    "model": "AppNotificationsLog",
    "method": "CREATE",
    "data": [
      {
        "id": 1002,
        "app_notification_type": 3,
        "is_read": false,
        "icon": "article",
        "title": "New Article Published",
        "url": "/workspace-articles",
        "created": "'$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")'"
      }
    ]
  }'

echo ""
echo ""

# Test 3: Send an export notification
echo "📊 Sending export notification..."
curl -X POST http://localhost:8080/notify \
  -H "Content-Type: application/json" \
  -d '{
    "model": "AppNotificationsLog",
    "method": "CREATE",
    "data": [
      {
        "id": 1003,
        "app_notification_type": 2,
        "is_read": false,
        "icon": "download",
        "title": "Export Ready",
        "url": "/exports",
        "created": "'$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")'"
      }
    ]
  }'

echo ""
echo ""

# Test 4: Check server health
echo "🏥 Checking WebSocket server health..."
curl -X GET http://localhost:8080/health

echo ""
echo ""
echo "✅ Tests completed!"
echo "Check the Monitora app notifications bell icon for new notifications."
