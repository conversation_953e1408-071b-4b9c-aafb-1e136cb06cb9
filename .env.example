# Server Configuration
PORT=8080
NODE_ENV=development

# WebSocket Configuration
WS_PORT=8081
WS_HOST=localhost

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Logging
LOG_LEVEL=info
LOG_PRETTY=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Health Check
HEALTH_CHECK_INTERVAL=30000

# Database (if needed in the future)
# DATABASE_URL=postgresql://user:password@localhost:5432/mediaboard_ws
