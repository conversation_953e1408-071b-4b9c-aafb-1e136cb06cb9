{"name": "mediaboard-ws", "version": "1.0.0", "description": "WebSocket server for real-time notifications in Monitora application", "main": "dist/server.js", "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "test:notifications": "node scripts/test-notifications.js", "test:connection": "node scripts/test-connection.js", "test:msw": "node scripts/test-msw.js", "test:frontend": "node scripts/test-frontend-integration.js", "setup:mitmproxy": "node scripts/setup-mitmproxy-ws.js", "test:mitmproxy": "node scripts/test-mitmproxy-ws.js"}, "keywords": ["websocket", "notifications", "real-time", "monitora"], "author": "Monitora Team", "license": "UNLICENSED", "dependencies": {"cors": "^2.8.5", "express": "^4.21.2", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "uuid": "^10.0.0", "ws": "^8.18.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.15.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.13", "msw": "^2.10.2", "tsx": "^4.19.2", "typescript": "^5.7.3"}, "engines": {"node": ">=22"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}