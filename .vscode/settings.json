{
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "[nginx]": {
    "editor.defaultFormatter": "ahmadalli.vscode-nginx-conf"
  },
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter"
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.gotoLocation.multipleDefinitions": "goto",
  "files.associations": {
    "*.json": "json",
    ".env*": "properties",
    ".swcrc": "json"
  },
  "search.exclude": {
    "**/*.snap": true
  },
  "stylelint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
    //
  ]
}
