{
  "recommendations": [
    // Linting, formatting
    "ahmadalli.vscode-nginx-conf",
    "dbaeumer.vscode-eslint",
    "editorconfig.editorconfig",
    "esbenp.prettier-vscode",
    "exiasr.hadolint",
    "github.vscode-github-actions",
    "grit.grit-vscode",
    "ms-python.black-formatter",
    "stylelint.vscode-stylelint",
    "usernamehw.errorlens",
    "yoavbls.pretty-ts-errors",

    // Syntax highlighting
    "mikestead.dotenv",
    "mrorz.language-gettext",
    "styled-components.vscode-styled-components",

    // Comments
    "aaron-bond.better-comments",
    "gruntfuggly.todo-tree",
    "stkb.rewrap",

    // Editing
    "formulahendry.auto-complete-tag",

    // Development, debugging
    "firefox-devtools.vscode-firefox-debug",
    "ms-azuretools.vscode-docker",
    "ms-vscode-remote.remote-containers",
    "ms-vscode.vscode-js-profile-flame",
    "ms-vsliveshare.vsliveshare"
  ]
}
